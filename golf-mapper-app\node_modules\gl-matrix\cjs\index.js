"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.vec4 = exports.vec3 = exports.vec2 = exports.quat2 = exports.quat = exports.mat4 = exports.mat3 = exports.mat2d = exports.mat2 = exports.glMatrix = void 0;
var glMatrix = _interopRequireWildcard(require("./common.js"));
exports.glMatrix = glMatrix;
var mat2 = _interopRequireWildcard(require("./mat2.js"));
exports.mat2 = mat2;
var mat2d = _interopRequireWildcard(require("./mat2d.js"));
exports.mat2d = mat2d;
var mat3 = _interopRequireWildcard(require("./mat3.js"));
exports.mat3 = mat3;
var mat4 = _interopRequireWildcard(require("./mat4.js"));
exports.mat4 = mat4;
var quat = _interopRequireWildcard(require("./quat.js"));
exports.quat = quat;
var quat2 = _interopRequireWildcard(require("./quat2.js"));
exports.quat2 = quat2;
var vec2 = _interopRequireWildcard(require("./vec2.js"));
exports.vec2 = vec2;
var vec3 = _interopRequireWildcard(require("./vec3.js"));
exports.vec3 = vec3;
var vec4 = _interopRequireWildcard(require("./vec4.js"));
exports.vec4 = vec4;
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, "default": e }; if (null === e || "object" != _typeof(e) && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }