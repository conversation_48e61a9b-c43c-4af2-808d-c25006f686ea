import { Link } from 'react-router-dom'

const Home = () => {
  return (
    <div className="welcome-section">
      <h1>Welcome to Frisbee Golf Mapper</h1>
      <p>
        Create and map your frisbee golf courses with precision. Design fairways, 
        add walking paths, and save your course layouts for future reference.
      </p>
      
      <div style={{ marginBottom: '2rem' }}>
        <Link to="/create" className="cta-button">
          🎯 Create New Course
        </Link>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '2rem', maxWidth: '800px', margin: '0 auto' }}>
        <div style={{ textAlign: 'center', padding: '1.5rem', backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🗺️</div>
          <h3 style={{ color: '#2c3e50', marginBottom: '0.5rem' }}>Interactive Mapping</h3>
          <p style={{ color: '#7f8c8d' }}>Design courses with satellite imagery and precise point placement</p>
        </div>

        <div style={{ textAlign: 'center', padding: '1.5rem', backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🎯</div>
          <h3 style={{ color: '#2c3e50', marginBottom: '0.5rem' }}>Fairway Design</h3>
          <p style={{ color: '#7f8c8d' }}>Create fairways with start points, end points, and custom curves</p>
        </div>

        <div style={{ textAlign: 'center', padding: '1.5rem', backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🚶</div>
          <h3 style={{ color: '#2c3e50', marginBottom: '0.5rem' }}>Walking Paths</h3>
          <p style={{ color: '#7f8c8d' }}>Add walking paths to connect holes and improve course flow</p>
        </div>
      </div>

      <div style={{ marginTop: '3rem', textAlign: 'center' }}>
        <Link to="/courses" style={{ color: '#3498db', textDecoration: 'none', fontSize: '1.1rem' }}>
          📋 View Existing Courses →
        </Link>
      </div>
    </div>
  )
}

export default Home
