import { useEffect, useRef, useState } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

// You'll need to get a free API key from https://www.mapbox.com/
mapboxgl.accessToken =
  "pk.eyJ1IjoiZXhhbXBsZSIsImEiOiJjazZqb2V6YWowMDAwM29wbnl1dGRxZGZoIn0.example"; // Replace with your actual token

const MapView = () => {
  const mapContainer = useRef(null);
  const map = useRef(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  // State for tracking points and lines
  const [startPoint, setStartPoint] = useState(null);
  const [endPoint, setEndPoint] = useState(null);
  const [fairwayLine, setFairwayLine] = useState(null);
  const [walkingPaths, setWalkingPaths] = useState([]);
  const [savedFairways, setSavedFairways] = useState([]);

  // State for control box
  const [currentMode, setCurrentMode] = useState("idle"); // idle, selecting-start, selecting-end, creating-walking-path
  const [statusMessage, setStatusMessage] = useState(
    "Click on map to place start point. Click End Point button when ready."
  );

  useEffect(() => {
    if (map.current) return; // Initialize map only once

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: "mapbox://styles/mapbox/satellite-v9",
      center: [-122.4194, 37.7749], // San Francisco coordinates (sample golf course area)
      zoom: 16,
    });

    map.current.on("load", () => {
      setMapLoaded(true);

      // Add click handler for placing points
      map.current.on("click", handleMapClick);
    });

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, []);

  const handleMapClick = (e) => {
    const { lng, lat } = e.lngLat;

    if (currentMode === "selecting-start") {
      placeStartPoint(lng, lat);
    } else if (currentMode === "selecting-end") {
      placeEndPoint(lng, lat);
    } else if (currentMode === "creating-walking-path") {
      createWalkingPath(lng, lat);
    }
  };

  const placeStartPoint = (lng, lat) => {
    // Remove existing start point if any
    if (startPoint) {
      startPoint.remove();
    }

    // Create start point marker (small rectangle)
    const el = document.createElement("div");
    el.style.width = "12px";
    el.style.height = "12px";
    el.style.backgroundColor = "#ff0000";
    el.style.border = "2px solid white";

    const marker = new mapboxgl.Marker(el)
      .setLngLat([lng, lat])
      .addTo(map.current);

    setStartPoint(marker);
    setCurrentMode("idle");
    setStatusMessage(
      'Start point saved! Click "Continue Fairway" to select end point and complete the fairway.'
    );
  };

  const placeEndPoint = (lng, lat) => {
    // Remove existing end point if any
    if (endPoint) {
      endPoint.remove();
    }

    // Create end point marker (golf ball icon)
    const el = document.createElement("div");
    el.innerHTML = "⛳";
    el.style.fontSize = "20px";

    const marker = new mapboxgl.Marker(el)
      .setLngLat([lng, lat])
      .addTo(map.current);

    setEndPoint(marker);

    // Draw line between start and end points
    if (startPoint) {
      drawFairwayLine();
    }

    setCurrentMode("idle");
    setStatusMessage(
      "Fairway is complete! CREATE WALKING PATH or Save Fairway."
    );
  };

  const drawFairwayLine = () => {
    if (!startPoint || !endPoint) return;

    const startCoords = startPoint.getLngLat();
    const endCoords = endPoint.getLngLat();

    const lineId = "current-fairway-line";

    // Remove existing line if any
    if (map.current.getSource(lineId)) {
      map.current.removeLayer(lineId);
      map.current.removeSource(lineId);
    }

    map.current.addSource(lineId, {
      type: "geojson",
      data: {
        type: "Feature",
        properties: {},
        geometry: {
          type: "LineString",
          coordinates: [
            [startCoords.lng, startCoords.lat],
            [endCoords.lng, endCoords.lat],
          ],
        },
      },
    });

    map.current.addLayer({
      id: lineId,
      type: "line",
      source: lineId,
      layout: {
        "line-join": "round",
        "line-cap": "round",
      },
      paint: {
        "line-color": "#0080ff",
        "line-width": 4,
      },
    });

    setFairwayLine(lineId);
  };

  const createWalkingPath = (lng, lat) => {
    if (!endPoint) return;

    const endCoords = endPoint.getLngLat();
    const pathId = `walking-path-${Date.now()}`;

    map.current.addSource(pathId, {
      type: "geojson",
      data: {
        type: "Feature",
        properties: {},
        geometry: {
          type: "LineString",
          coordinates: [
            [endCoords.lng, endCoords.lat],
            [lng, lat],
          ],
        },
      },
    });

    map.current.addLayer({
      id: pathId,
      type: "line",
      source: pathId,
      layout: {
        "line-join": "round",
        "line-cap": "round",
      },
      paint: {
        "line-color": "#ff8000",
        "line-width": 3,
        "line-dasharray": [2, 2],
      },
    });

    setWalkingPaths((prev) => [...prev, pathId]);
    setCurrentMode("idle");
    setStatusMessage("Walking path created!");
  };

  // Control box button handlers
  const handleStartPoint = () => {
    setCurrentMode("selecting-start");
    setStatusMessage("Click on map to place start point.");
  };

  const handleEndPoint = () => {
    if (!startPoint) {
      setStatusMessage("Please place a start point first!");
      return;
    }
    setCurrentMode("selecting-end");
    setStatusMessage("Tap on map to set end point");
  };

  const handleContinueFairway = () => {
    setCurrentMode("selecting-end");
    setStatusMessage("Select end point to complete the fairway");
  };

  const handleSaveFairway = () => {
    if (!fairwayLine) {
      setStatusMessage("No fairway to save!");
      return;
    }

    // Add current fairway to saved fairways
    setSavedFairways((prev) => [...prev, fairwayLine]);
    setStatusMessage("Fairway saved successfully!");

    // Reset for next fairway
    setStartPoint(null);
    setEndPoint(null);
    setFairwayLine(null);
  };

  const handleCreateWalkingPath = () => {
    if (!endPoint) {
      setStatusMessage("Please complete a fairway first!");
      return;
    }
    setCurrentMode("creating-walking-path");
    setStatusMessage(
      "Walking path started from last fairway end. Tap on map to set end point."
    );
  };

  const handleUndo = () => {
    if (walkingPaths.length > 0) {
      // Remove last walking path
      const lastPath = walkingPaths[walkingPaths.length - 1];
      if (map.current.getSource(lastPath)) {
        map.current.removeLayer(lastPath);
        map.current.removeSource(lastPath);
      }
      setWalkingPaths((prev) => prev.slice(0, -1));
      setStatusMessage("Last walking path removed.");
    } else if (endPoint) {
      // Remove end point and fairway line
      endPoint.remove();
      setEndPoint(null);
      if (fairwayLine && map.current.getSource(fairwayLine)) {
        map.current.removeLayer(fairwayLine);
        map.current.removeSource(fairwayLine);
      }
      setFairwayLine(null);
      setStatusMessage("End point removed.");
    } else if (startPoint) {
      // Remove start point
      startPoint.remove();
      setStartPoint(null);
      setStatusMessage("Start point removed.");
    }
    setCurrentMode("idle");
  };

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        height: "calc(100vh - 80px)",
      }}
    >
      <div ref={mapContainer} style={{ width: "100%", height: "100%" }} />

      {/* Control Box */}
      <div
        style={{
          position: "absolute",
          top: "20px",
          right: "20px",
          backgroundColor: "white",
          borderRadius: "8px",
          padding: "16px",
          boxShadow: "0 4px 12px rgba(0,0,0,0.3)",
          minWidth: "280px",
          zIndex: 1000,
        }}
      >
        {/* Status Message */}
        <div
          style={{
            marginBottom: "12px",
            fontSize: "14px",
            color: "#333",
            textAlign: "center",
            minHeight: "40px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {statusMessage}
        </div>

        {/* Continue Fairway Button (shown after start point is placed) */}
        {startPoint && !endPoint && currentMode === "idle" && (
          <div style={{ marginBottom: "12px", textAlign: "center" }}>
            <button
              onClick={handleContinueFairway}
              style={{
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "6px",
                padding: "8px 16px",
                cursor: "pointer",
                fontSize: "14px",
              }}
            >
              ▶ Continue Fairway
            </button>
          </div>
        )}

        {/* Button Grid */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: "8px",
          }}
        >
          {/* Start Point Button */}
          <button
            onClick={handleStartPoint}
            disabled={currentMode === "selecting-start"}
            style={{
              backgroundColor:
                currentMode === "selecting-start"
                  ? "#007bff"
                  : startPoint
                  ? "#6c757d"
                  : "#007bff",
              color: "white",
              border: "none",
              borderRadius: "6px",
              padding: "10px 8px",
              cursor: startPoint ? "default" : "pointer",
              fontSize: "12px",
              opacity: startPoint ? 0.7 : 1,
            }}
          >
            📍{" "}
            {startPoint
              ? "Start Point Created"
              : currentMode === "selecting-start"
              ? "Selecting Start..."
              : "Start Point"}
          </button>

          {/* End Point Button */}
          <button
            onClick={handleEndPoint}
            disabled={!startPoint || currentMode === "selecting-end"}
            style={{
              backgroundColor:
                currentMode === "selecting-end"
                  ? "#007bff"
                  : endPoint
                  ? "#6c757d"
                  : "#007bff",
              color: "white",
              border: "none",
              borderRadius: "6px",
              padding: "10px 8px",
              cursor: !startPoint || endPoint ? "default" : "pointer",
              fontSize: "12px",
              opacity: !startPoint || endPoint ? 0.7 : 1,
            }}
          >
            🎯 {endPoint ? "End Point ✓" : "End Point"}
          </button>

          {/* Add Curve Button */}
          <button
            disabled={true}
            style={{
              backgroundColor: "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "6px",
              padding: "10px 8px",
              cursor: "default",
              fontSize: "12px",
              opacity: 0.7,
            }}
          >
            ↗️ Add Curve
          </button>

          {/* Save Fairway Button */}
          <button
            onClick={handleSaveFairway}
            disabled={!fairwayLine}
            style={{
              backgroundColor: fairwayLine ? "#28a745" : "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "6px",
              padding: "10px 8px",
              cursor: fairwayLine ? "pointer" : "default",
              fontSize: "12px",
              opacity: fairwayLine ? 1 : 0.7,
            }}
          >
            💾 Save Fairway
          </button>
        </div>

        {/* Full Width Buttons */}
        <div
          style={{
            marginTop: "8px",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
          }}
        >
          <button
            onClick={handleCreateWalkingPath}
            disabled={!endPoint}
            style={{
              backgroundColor: endPoint ? "#28a745" : "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "6px",
              padding: "10px",
              cursor: endPoint ? "pointer" : "default",
              fontSize: "12px",
              opacity: endPoint ? 1 : 0.7,
              width: "100%",
            }}
          >
            🚶 CREATE WALKING PATH!
          </button>

          <button
            onClick={handleUndo}
            style={{
              backgroundColor: "#fd7e14",
              color: "white",
              border: "none",
              borderRadius: "6px",
              padding: "10px",
              cursor: "pointer",
              fontSize: "12px",
              width: "100%",
            }}
          >
            ↶ Undo
          </button>
        </div>
      </div>
    </div>
  );
};

export default MapView;
