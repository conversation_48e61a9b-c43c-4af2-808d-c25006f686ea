!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(t="undefined"!=typeof globalThis?globalThis:t||self).geojsonvt=n()}(this,(function(){"use strict";function t(e,o,i,s){let l=s;const r=o+(i-o>>1);let u,f=i-o;const c=e[o],a=e[o+1],m=e[i],g=e[i+1];for(let t=o+3;t<i;t+=3){const o=n(e[t],e[t+1],c,a,m,g);if(o>l)u=t,l=o;else if(o===l){const n=Math.abs(t-r);n<f&&(u=t,f=n)}}l>s&&(u-o>3&&t(e,o,u,s),e[u+2]=l,i-u>3&&t(e,u,i,s))}function n(t,n,e,o,i,s){let l=i-e,r=s-o;if(0!==l||0!==r){const u=((t-e)*l+(n-o)*r)/(l*l+r*r);u>1?(e=i,o=s):u>0&&(e+=l*u,o+=r*u)}return l=t-e,r=n-o,l*l+r*r}function e(t,n,e,i){const s={id:null==t?null:t,type:n,geometry:e,tags:i,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0};if("Point"===n||"MultiPoint"===n||"LineString"===n)o(s,e);else if("Polygon"===n)o(s,e[0]);else if("MultiLineString"===n)for(const t of e)o(s,t);else if("MultiPolygon"===n)for(const t of e)o(s,t[0]);return s}function o(t,n){for(let e=0;e<n.length;e+=3)t.minX=Math.min(t.minX,n[e]),t.minY=Math.min(t.minY,n[e+1]),t.maxX=Math.max(t.maxX,n[e]),t.maxY=Math.max(t.maxY,n[e+1])}function i(t,n,o,u){if(!n.geometry)return;const f=n.geometry.coordinates;if(f&&0===f.length)return;const c=n.geometry.type,a=Math.pow(o.tolerance/((1<<o.maxZoom)*o.extent),2);let m=[],g=n.id;if(o.promoteId?g=n.properties[o.promoteId]:o.generateId&&(g=u||0),"Point"===c)s(f,m);else if("MultiPoint"===c)for(const t of f)s(t,m);else if("LineString"===c)l(f,m,a,!1);else if("MultiLineString"===c){if(o.lineMetrics){for(const o of f)m=[],l(o,m,a,!1),t.push(e(g,"LineString",m,n.properties));return}r(f,m,a,!1)}else if("Polygon"===c)r(f,m,a,!0);else{if("MultiPolygon"!==c){if("GeometryCollection"===c){for(const e of n.geometry.geometries)i(t,{id:g,geometry:e,properties:n.properties},o,u);return}throw new Error("Input data is not a valid GeoJSON object.")}for(const t of f){const n=[];r(t,n,a,!0),m.push(n)}}t.push(e(g,c,m,n.properties))}function s(t,n){n.push(u(t[0]),f(t[1]),0)}function l(n,e,o,i){let s,l,r=0;for(let t=0;t<n.length;t++){const o=u(n[t][0]),c=f(n[t][1]);e.push(o,c,0),t>0&&(r+=i?(s*c-o*l)/2:Math.sqrt(Math.pow(o-s,2)+Math.pow(c-l,2))),s=o,l=c}const c=e.length-3;e[2]=1,t(e,0,c,o),e[c+2]=1,e.size=Math.abs(r),e.start=0,e.end=e.size}function r(t,n,e,o){for(let i=0;i<t.length;i++){const s=[];l(t[i],s,e,o),n.push(s)}}function u(t){return t/360+.5}function f(t){const n=Math.sin(t*Math.PI/180),e=.5-.25*Math.log((1+n)/(1-n))/Math.PI;return e<0?0:e>1?1:e}function c(t,n,o,i,s,l,r,u){if(i/=n,l>=(o/=n)&&r<i)return t;if(r<o||l>=i)return null;const f=[];for(const n of t){const t=n.geometry;let l=n.type;const r=0===s?n.minX:n.minY,c=0===s?n.maxX:n.maxY;if(r>=o&&c<i){f.push(n);continue}if(c<o||r>=i)continue;let g=[];if("Point"===l||"MultiPoint"===l)a(t,g,o,i,s);else if("LineString"===l)m(t,g,o,i,s,!1,u.lineMetrics);else if("MultiLineString"===l)h(t,g,o,i,s,!1);else if("Polygon"===l)h(t,g,o,i,s,!0);else if("MultiPolygon"===l)for(const n of t){const t=[];h(n,t,o,i,s,!0),t.length&&g.push(t)}if(g.length){if(u.lineMetrics&&"LineString"===l){for(const t of g)f.push(e(n.id,l,t,n.tags));continue}"LineString"!==l&&"MultiLineString"!==l||(1===g.length?(l="LineString",g=g[0]):l="MultiLineString"),"Point"!==l&&"MultiPoint"!==l||(l=3===g.length?"Point":"MultiPoint"),f.push(e(n.id,l,g,n.tags))}}return f.length?f:null}function a(t,n,e,o,i){for(let s=0;s<t.length;s+=3){const l=t[s+i];l>=e&&l<=o&&p(n,t[s],t[s+1],t[s+2])}}function m(t,n,e,o,i,s,l){let r=g(t);const u=0===i?d:x;let f,c,a=t.start;for(let m=0;m<t.length-3;m+=3){const h=t[m],d=t[m+1],x=t[m+2],M=t[m+3],y=t[m+4],P=0===i?h:d,S=0===i?M:y;let Y=!1;l&&(f=Math.sqrt(Math.pow(h-M,2)+Math.pow(d-y,2))),P<e?S>e&&(c=u(r,h,d,M,y,e),l&&(r.start=a+f*c)):P>o?S<o&&(c=u(r,h,d,M,y,o),l&&(r.start=a+f*c)):p(r,h,d,x),S<e&&P>=e&&(c=u(r,h,d,M,y,e),Y=!0),S>o&&P<=o&&(c=u(r,h,d,M,y,o),Y=!0),!s&&Y&&(l&&(r.end=a+f*c),n.push(r),r=g(t)),l&&(a+=f)}let m=t.length-3;const h=t[m],M=t[m+1],y=t[m+2],P=0===i?h:M;P>=e&&P<=o&&p(r,h,M,y),m=r.length-3,s&&m>=3&&(r[m]!==r[0]||r[m+1]!==r[1])&&p(r,r[0],r[1],r[2]),r.length&&n.push(r)}function g(t){const n=[];return n.size=t.size,n.start=t.start,n.end=t.end,n}function h(t,n,e,o,i,s){for(const l of t)m(l,n,e,o,i,s,!1)}function p(t,n,e,o){t.push(n,e,o)}function d(t,n,e,o,i,s){const l=(s-n)/(o-n);return p(t,s,e+(i-e)*l,1),l}function x(t,n,e,o,i,s){const l=(s-e)/(i-e);return p(t,n+(o-n)*l,s,1),l}function M(t,n){const o=[];for(let i=0;i<t.length;i++){const s=t[i],l=s.type;let r;if("Point"===l||"MultiPoint"===l||"LineString"===l)r=y(s.geometry,n);else if("MultiLineString"===l||"Polygon"===l){r=[];for(const t of s.geometry)r.push(y(t,n))}else if("MultiPolygon"===l){r=[];for(const t of s.geometry){const e=[];for(const o of t)e.push(y(o,n));r.push(e)}}o.push(e(s.id,l,r,s.tags))}return o}function y(t,n){const e=[];e.size=t.size,void 0!==t.start&&(e.start=t.start,e.end=t.end);for(let o=0;o<t.length;o+=3)e.push(t[o]+n,t[o+1],t[o+2]);return e}function P(t,n){if(t.transformed)return t;const e=1<<t.z,o=t.x,i=t.y;for(const s of t.features){const t=s.geometry,l=s.type;if(s.geometry=[],1===l)for(let l=0;l<t.length;l+=2)s.geometry.push(S(t[l],t[l+1],n,e,o,i));else for(let l=0;l<t.length;l++){const r=[];for(let s=0;s<t[l].length;s+=2)r.push(S(t[l][s],t[l][s+1],n,e,o,i));s.geometry.push(r)}}return t.transformed=!0,t}function S(t,n,e,o,i,s){return[Math.round(e*(t*o-i)),Math.round(e*(n*o-s))]}function Y(t,n,e,o,i){const s=n===i.maxZoom?0:i.tolerance/((1<<n)*i.extent),l={features:[],numPoints:0,numSimplified:0,numFeatures:t.length,source:null,x:e,y:o,z:n,transformed:!1,minX:2,minY:1,maxX:-1,maxY:0};for(const n of t)X(l,n,s,i);return l}function X(t,n,e,o){const i=n.geometry,s=n.type,l=[];if(t.minX=Math.min(t.minX,n.minX),t.minY=Math.min(t.minY,n.minY),t.maxX=Math.max(t.maxX,n.maxX),t.maxY=Math.max(t.maxY,n.maxY),"Point"===s||"MultiPoint"===s)for(let n=0;n<i.length;n+=3)l.push(i[n],i[n+1]),t.numPoints++,t.numSimplified++;else if("LineString"===s)L(l,i,t,e,!1,!1);else if("MultiLineString"===s||"Polygon"===s)for(let n=0;n<i.length;n++)L(l,i[n],t,e,"Polygon"===s,0===n);else if("MultiPolygon"===s)for(let n=0;n<i.length;n++){const o=i[n];for(let n=0;n<o.length;n++)L(l,o[n],t,e,!0,0===n)}if(l.length){let e=n.tags||null;if("LineString"===s&&o.lineMetrics){e={};for(const t in n.tags)e[t]=n.tags[t];e.mapbox_clip_start=i.start/i.size,e.mapbox_clip_end=i.end/i.size}const r={geometry:l,type:"Polygon"===s||"MultiPolygon"===s?3:"LineString"===s||"MultiLineString"===s?2:1,tags:e};null!==n.id&&(r.id=n.id),t.features.push(r)}}function L(t,n,e,o,i,s){const l=o*o;if(o>0&&n.size<(i?l:o))return void(e.numPoints+=n.length/3);const r=[];for(let t=0;t<n.length;t+=3)(0===o||n[t+2]>l)&&(e.numSimplified++,r.push(n[t],n[t+1])),e.numPoints++;i&&function(t,n){let e=0;for(let n=0,o=t.length,i=o-2;n<o;i=n,n+=2)e+=(t[n]-t[i])*(t[n+1]+t[i+1]);if(e>0===n)for(let n=0,e=t.length;n<e/2;n+=2){const o=t[n],i=t[n+1];t[n]=t[e-2-n],t[n+1]=t[e-1-n],t[e-2-n]=o,t[e-1-n]=i}}(r,s),t.push(r)}const b={maxZoom:14,indexMaxZoom:5,indexMaxPoints:1e5,tolerance:3,extent:4096,buffer:64,lineMetrics:!1,promoteId:null,generateId:!1,debug:0};class z{constructor(t,n){const e=(n=this.options=function(t,n){for(const e in n)t[e]=n[e];return t}(Object.create(b),n)).debug;if(e&&console.time("preprocess data"),n.maxZoom<0||n.maxZoom>24)throw new Error("maxZoom should be in the 0-24 range");if(n.promoteId&&n.generateId)throw new Error("promoteId and generateId cannot be used together.");let o=function(t,n){const e=[];if("FeatureCollection"===t.type)for(let o=0;o<t.features.length;o++)i(e,t.features[o],n,o);else"Feature"===t.type?i(e,t,n):i(e,{geometry:t},n);return e}(t,n);this.tiles={},this.tileCoords=[],e&&(console.timeEnd("preprocess data"),console.log("index: maxZoom: %d, maxPoints: %d",n.indexMaxZoom,n.indexMaxPoints),console.time("generate tiles"),this.stats={},this.total=0),o=function(t,n){const e=n.buffer/n.extent;let o=t;const i=c(t,1,-1-e,e,0,-1,2,n),s=c(t,1,1-e,2+e,0,-1,2,n);return(i||s)&&(o=c(t,1,-e,1+e,0,-1,2,n)||[],i&&(o=M(i,1).concat(o)),s&&(o=o.concat(M(s,-1)))),o}(o,n),o.length&&this.splitTile(o,0,0,0),e&&(o.length&&console.log("features: %d, points: %d",this.tiles[0].numFeatures,this.tiles[0].numPoints),console.timeEnd("generate tiles"),console.log("tiles generated:",this.total,JSON.stringify(this.stats)))}splitTile(t,n,e,o,i,s,l){const r=[t,n,e,o],u=this.options,f=u.debug;for(;r.length;){o=r.pop(),e=r.pop(),n=r.pop(),t=r.pop();const a=1<<n,m=w(n,e,o);let g=this.tiles[m];if(!g&&(f>1&&console.time("creation"),g=this.tiles[m]=Y(t,n,e,o,u),this.tileCoords.push({z:n,x:e,y:o}),f)){f>1&&(console.log("tile z%d-%d-%d (features: %d, points: %d, simplified: %d)",n,e,o,g.numFeatures,g.numPoints,g.numSimplified),console.timeEnd("creation"));const t=`z${n}`;this.stats[t]=(this.stats[t]||0)+1,this.total++}if(g.source=t,null==i){if(n===u.indexMaxZoom||g.numPoints<=u.indexMaxPoints)continue}else{if(n===u.maxZoom||n===i)continue;if(null!=i){const t=i-n;if(e!==s>>t||o!==l>>t)continue}}if(g.source=null,0===t.length)continue;f>1&&console.time("clipping");const h=.5*u.buffer/u.extent,p=.5-h,d=.5+h,x=1+h;let M=null,y=null,P=null,S=null,X=c(t,a,e-h,e+d,0,g.minX,g.maxX,u),L=c(t,a,e+p,e+x,0,g.minX,g.maxX,u);t=null,X&&(M=c(X,a,o-h,o+d,1,g.minY,g.maxY,u),y=c(X,a,o+p,o+x,1,g.minY,g.maxY,u),X=null),L&&(P=c(L,a,o-h,o+d,1,g.minY,g.maxY,u),S=c(L,a,o+p,o+x,1,g.minY,g.maxY,u),L=null),f>1&&console.timeEnd("clipping"),r.push(M||[],n+1,2*e,2*o),r.push(y||[],n+1,2*e,2*o+1),r.push(P||[],n+1,2*e+1,2*o),r.push(S||[],n+1,2*e+1,2*o+1)}}getTile(t,n,e){t=+t,n=+n,e=+e;const o=this.options,{extent:i,debug:s}=o;if(t<0||t>24)return null;const l=1<<t,r=w(t,n=n+l&l-1,e);if(this.tiles[r])return P(this.tiles[r],i);s>1&&console.log("drilling down to z%d-%d-%d",t,n,e);let u,f=t,c=n,a=e;for(;!u&&f>0;)f--,c>>=1,a>>=1,u=this.tiles[w(f,c,a)];return u&&u.source?(s>1&&(console.log("found parent tile z%d-%d-%d",f,c,a),console.time("drilling down")),this.splitTile(u.source,f,c,a,t,n,e),s>1&&console.timeEnd("drilling down"),this.tiles[r]?P(this.tiles[r],i):null):null}}function w(t,n,e){return 32*((1<<t)*e+n)+t}return function(t,n){return new z(t,n)}}));
