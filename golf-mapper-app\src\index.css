* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-header {
  background-color: #2c3e50;
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 1000;
}

.app-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background-color: #34495e;
  color: white;
  padding: 1rem;
  box-shadow: 2px 0 4px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.sidebar nav ul {
  list-style: none;
}

.sidebar nav ul li {
  margin-bottom: 0.5rem;
}

.sidebar nav ul li a {
  color: #ecf0f1;
  text-decoration: none;
  padding: 0.75rem 1rem;
  display: block;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.sidebar nav ul li a:hover,
.sidebar nav ul li a.active {
  background-color: #3498db;
}

.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background-color: white;
}

.map-container {
  width: 100%;
  height: 500px;
  background-color: #e8f5e8;
  border: 2px dashed #27ae60;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-bottom: 1rem;
  color: #27ae60;
  font-size: 1.2rem;
}

.toolbar {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.toolbar button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.toolbar button.primary {
  background-color: #3498db;
  color: white;
}

.toolbar button.primary:hover {
  background-color: #2980b9;
}

.toolbar button.success {
  background-color: #27ae60;
  color: white;
}

.toolbar button.success:hover {
  background-color: #229954;
}

.toolbar button.warning {
  background-color: #f39c12;
  color: white;
}

.toolbar button.warning:hover {
  background-color: #e67e22;
}

.toolbar button.secondary {
  background-color: #95a5a6;
  color: white;
}

.toolbar button.secondary:hover {
  background-color: #7f8c8d;
}

.status-bar {
  background-color: #ecf0f1;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  border-left: 4px solid #3498db;
}

.status-bar.success {
  border-left-color: #27ae60;
  background-color: #d5f4e6;
  color: #27ae60;
}

.status-bar.error {
  border-left-color: #e74c3c;
  background-color: #fdf2f2;
  color: #e74c3c;
}

.status-bar.warning {
  border-left-color: #f39c12;
  background-color: #fef9e7;
  color: #f39c12;
}

.course-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.course-card {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.course-card h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.course-card p {
  color: #7f8c8d;
  margin-bottom: 0.5rem;
}

.welcome-section {
  text-align: center;
  padding: 3rem 2rem;
}

.welcome-section h1 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 2.5rem;
}

.welcome-section p {
  color: #7f8c8d;
  font-size: 1.2rem;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button {
  background-color: #3498db;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: background-color 0.2s;
}

.cta-button:hover {
  background-color: #2980b9;
}

@media (max-width: 768px) {
  .app-body {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
  }
  
  .sidebar nav ul {
    display: flex;
    gap: 1rem;
  }
  
  .sidebar nav ul li {
    margin-bottom: 0;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .toolbar {
    justify-content: center;
  }
  
  .course-list {
    grid-template-columns: 1fr;
  }
}
