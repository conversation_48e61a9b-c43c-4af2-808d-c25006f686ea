const About = () => {
  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      <h2 style={{ color: '#2c3e50', marginBottom: '2rem' }}>About Frisbee Golf Mapper</h2>
      
      <div style={{ backgroundColor: 'white', padding: '2rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '2rem' }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '1rem' }}>What is Frisbee Golf Mapper?</h3>
        <p style={{ color: '#7f8c8d', lineHeight: '1.6', marginBottom: '1rem' }}>
          Frisbee Golf Mapper is a comprehensive web application designed to help disc golf enthusiasts, 
          course designers, and park managers create, design, and manage frisbee golf courses with precision and ease.
        </p>
        <p style={{ color: '#7f8c8d', lineHeight: '1.6' }}>
          Whether you're planning a new course layout, documenting an existing course, or sharing course 
          information with other players, our intuitive mapping tools make the process simple and accurate.
        </p>
      </div>

      <div style={{ backgroundColor: 'white', padding: '2rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '2rem' }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '1rem' }}>Key Features</h3>
        <ul style={{ color: '#7f8c8d', lineHeight: '1.8', paddingLeft: '1.5rem' }}>
          <li><strong>Interactive Mapping:</strong> Use satellite imagery to design courses with real-world accuracy</li>
          <li><strong>Fairway Design:</strong> Create fairways with precise start and end points, including curved paths</li>
          <li><strong>Walking Paths:</strong> Design connecting paths between holes for optimal course flow</li>
          <li><strong>Course Management:</strong> Save, edit, and organize multiple course layouts</li>
          <li><strong>Export Options:</strong> Share course designs with others or export for printing</li>
          <li><strong>Mobile Responsive:</strong> Access your courses from any device</li>
        </ul>
      </div>

      <div style={{ backgroundColor: 'white', padding: '2rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '2rem' }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '1rem' }}>How to Use</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
          <div>
            <h4 style={{ color: '#3498db', marginBottom: '0.5rem' }}>1. Create Course</h4>
            <p style={{ color: '#7f8c8d', fontSize: '0.9rem' }}>
              Start by giving your course a name and description, then use the mapping tools to design your layout.
            </p>
          </div>
          <div>
            <h4 style={{ color: '#3498db', marginBottom: '0.5rem' }}>2. Design Holes</h4>
            <p style={{ color: '#7f8c8d', fontSize: '0.9rem' }}>
              Place tee positions, basket locations, and define fairway boundaries for each hole.
            </p>
          </div>
          <div>
            <h4 style={{ color: '#3498db', marginBottom: '0.5rem' }}>3. Add Paths</h4>
            <p style={{ color: '#7f8c8d', fontSize: '0.9rem' }}>
              Create walking paths to connect holes and improve the overall course experience.
            </p>
          </div>
          <div>
            <h4 style={{ color: '#3498db', marginBottom: '0.5rem' }}>4. Save & Share</h4>
            <p style={{ color: '#7f8c8d', fontSize: '0.9rem' }}>
              Save your course design and share it with other players or course managers.
            </p>
          </div>
        </div>
      </div>

      <div style={{ backgroundColor: 'white', padding: '2rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '1rem' }}>Technology</h3>
        <p style={{ color: '#7f8c8d', lineHeight: '1.6', marginBottom: '1rem' }}>
          Built with modern web technologies including React, React Router, and integrated mapping services 
          to provide a smooth and responsive user experience across all devices.
        </p>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          <span style={{ backgroundColor: '#3498db', color: 'white', padding: '0.25rem 0.75rem', borderRadius: '12px', fontSize: '0.9rem' }}>
            React
          </span>
          <span style={{ backgroundColor: '#27ae60', color: 'white', padding: '0.25rem 0.75rem', borderRadius: '12px', fontSize: '0.9rem' }}>
            Vite
          </span>
          <span style={{ backgroundColor: '#f39c12', color: 'white', padding: '0.25rem 0.75rem', borderRadius: '12px', fontSize: '0.9rem' }}>
            React Router
          </span>
          <span style={{ backgroundColor: '#9b59b6', color: 'white', padding: '0.25rem 0.75rem', borderRadius: '12px', fontSize: '0.9rem' }}>
            Mapping APIs
          </span>
        </div>
      </div>
    </div>
  )
}

export default About
