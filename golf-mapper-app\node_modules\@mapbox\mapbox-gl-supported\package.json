{"name": "@mapbox/mapbox-gl-supported", "version": "3.0.0", "description": "A library to determine if a browser supports Mapbox GL JS", "main": "index.js", "typings": "./index.d.ts", "scripts": {"test": "eslint index.js", "build": "browserify -s mapboxgl index.js | uglifyjs -c -m -o mapbox-gl-supported.js", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/mapbox/mapbox-gl-supported.git"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/mapbox/mapbox-gl-supported/issues"}, "homepage": "https://github.com/mapbox/mapbox-gl-supported#readme", "files": ["mapbox-gl-supported.js", "index.d.ts"], "devDependencies": {"browserify": "^17.0.0", "eslint": "^8.23.0", "eslint-config-mourner": "^2.0.1", "uglify-js": "^3.17.4"}}