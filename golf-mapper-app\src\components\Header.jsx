const Header = () => {
  const handleIconClick = (iconName) => {
    console.log(`${iconName} icon clicked`);
    // Future functionality will be added here
  };

  return (
    <header className="app-header">
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <h1>Frisbee Golf Mapper</h1>
        <div style={{ display: "flex", gap: "1rem" }}>
          <button
            onClick={() => handleIconClick("Menu")}
            style={{
              background: "transparent",
              border: "none",
              color: "white",
              padding: "0.5rem",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "1.2rem",
            }}
            title="Menu"
          >
            ☰
          </button>
          <button
            onClick={() => handleIconClick("Settings")}
            style={{
              background: "transparent",
              border: "none",
              color: "white",
              padding: "0.5rem",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "1.2rem",
            }}
            title="Settings"
          >
            ⚙️
          </button>
          <button
            onClick={() => handleIconClick("Profile")}
            style={{
              background: "transparent",
              border: "none",
              color: "white",
              padding: "0.5rem",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "1.2rem",
            }}
            title="Profile"
          >
            👤
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
