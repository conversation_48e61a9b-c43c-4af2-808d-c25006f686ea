import { useState } from 'react'

const CoursesList = () => {
  // Dummy data for now - will be replaced with real data later
  const [courses] = useState([
    {
      id: 1,
      name: "Riverside Park Course",
      description: "A challenging 18-hole course along the river with water hazards and mature trees.",
      holes: 18,
      difficulty: "Intermediate",
      created: "2024-01-15",
      lastModified: "2024-01-20"
    },
    {
      id: 2,
      name: "Mountain View Course",
      description: "Scenic 9-hole course with elevation changes and beautiful mountain views.",
      holes: 9,
      difficulty: "Beginner",
      created: "2024-02-01",
      lastModified: "2024-02-01"
    },
    {
      id: 3,
      name: "Forest Trail Course",
      description: "Technical wooded course requiring precision throws through tight fairways.",
      holes: 12,
      difficulty: "Advanced",
      created: "2024-02-10",
      lastModified: "2024-02-15"
    }
  ])

  const handleEditCourse = (courseId) => {
    console.log(`Edit course ${courseId}`)
    // Future: Navigate to edit page
  }

  const handleDeleteCourse = (courseId) => {
    if (window.confirm('Are you sure you want to delete this course?')) {
      console.log(`Delete course ${courseId}`)
      // Future: Delete from database
    }
  }

  const handleViewCourse = (courseId) => {
    console.log(`View course ${courseId}`)
    // Future: Navigate to course view page
  }

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Beginner':
        return '#27ae60'
      case 'Intermediate':
        return '#f39c12'
      case 'Advanced':
        return '#e74c3c'
      default:
        return '#7f8c8d'
    }
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h2 style={{ color: '#2c3e50' }}>My Courses</h2>
        <button 
          onClick={() => window.location.href = '/create'}
          style={{
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          ➕ Create New Course
        </button>
      </div>

      {courses.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '3rem', color: '#7f8c8d' }}>
          <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🏌️</div>
          <h3>No courses yet</h3>
          <p>Create your first frisbee golf course to get started!</p>
        </div>
      ) : (
        <div className="course-list">
          {courses.map(course => (
            <div key={course.id} className="course-card">
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                <h3>{course.name}</h3>
                <span 
                  style={{ 
                    backgroundColor: getDifficultyColor(course.difficulty),
                    color: 'white',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '12px',
                    fontSize: '0.8rem'
                  }}
                >
                  {course.difficulty}
                </span>
              </div>
              
              <p>{course.description}</p>
              
              <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem', fontSize: '0.9rem', color: '#7f8c8d' }}>
                <span>🏌️ {course.holes} holes</span>
                <span>📅 Created: {course.created}</span>
              </div>
              
              <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                <button
                  onClick={() => handleViewCourse(course.id)}
                  style={{
                    backgroundColor: '#3498db',
                    color: 'white',
                    border: 'none',
                    padding: '0.5rem 1rem',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '0.9rem'
                  }}
                >
                  👁️ View
                </button>
                <button
                  onClick={() => handleEditCourse(course.id)}
                  style={{
                    backgroundColor: '#f39c12',
                    color: 'white',
                    border: 'none',
                    padding: '0.5rem 1rem',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '0.9rem'
                  }}
                >
                  ✏️ Edit
                </button>
                <button
                  onClick={() => handleDeleteCourse(course.id)}
                  style={{
                    backgroundColor: '#e74c3c',
                    color: 'white',
                    border: 'none',
                    padding: '0.5rem 1rem',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '0.9rem'
                  }}
                >
                  🗑️ Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default CoursesList
