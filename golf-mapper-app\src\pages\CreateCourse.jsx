import { useState } from 'react'
import MapView from '../components/MapView'
import Toolbar from '../components/Toolbar'
import CourseControls from '../components/CourseControls'
import StatusBar from '../components/StatusBar'

const CreateCourse = () => {
  const [statusMessage, setStatusMessage] = useState('')
  const [statusType, setStatusType] = useState('info')

  // This will be used to show status messages from various actions
  const showStatus = (message, type = 'info') => {
    setStatusMessage(message)
    setStatusType(type)
  }

  return (
    <div>
      <h2 style={{ marginBottom: '1.5rem', color: '#2c3e50' }}>Create New Course</h2>
      
      <CourseControls />
      
      <div style={{ marginBottom: '1rem' }}>
        <h3 style={{ color: '#2c3e50', marginBottom: '1rem' }}>Course Design</h3>
        <MapView />
      </div>
      
      <div style={{ marginBottom: '1rem' }}>
        <h4 style={{ color: '#2c3e50', marginBottom: '0.5rem' }}>Design Tools</h4>
        <Toolbar />
      </div>

      <StatusBar 
        message={statusMessage} 
        type={statusType}
      />

      <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
        <h4 style={{ color: '#2c3e50', marginBottom: '0.5rem' }}>Instructions:</h4>
        <ol style={{ color: '#7f8c8d', paddingLeft: '1.5rem' }}>
          <li>Enter course name and description above</li>
          <li>Click "Start Point" to place the tee position</li>
          <li>Click "End Point" to place the basket position</li>
          <li>Use "Add Curve" to create curved fairways</li>
          <li>Click "Save Fairway" to save the current hole</li>
          <li>Use "Walking Path" to connect holes</li>
          <li>Save the complete course when finished</li>
        </ol>
      </div>
    </div>
  )
}

export default CreateCourse
