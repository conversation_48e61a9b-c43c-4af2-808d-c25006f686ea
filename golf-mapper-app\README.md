# Frisbee Golf Mapper

A web application for creating and mapping frisbee golf courses with interactive satellite maps.

## Features

- **Interactive Satellite Map**: Uses Mapbox GL JS for high-quality satellite imagery
- **Course Design Tools**: Place start points, end points, and create fairways
- **Walking Path Creation**: Add walking paths between holes
- **Real-time Feedback**: Control box with status messages and button states
- **Responsive Design**: Works on desktop and mobile devices

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone or download this project
2. Navigate to the project directory:
   ```bash
   cd golf-mapper-app
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Open your browser and go to `http://localhost:3000`

### Mapbox Setup (Optional)

The app currently uses a demo Mapbox token. For production use:

1. Sign up for a free account at [mapbox.com](https://www.mapbox.com/)
2. Get your access token from the account dashboard
3. Replace the token in `src/components/MapView.jsx`:
   ```javascript
   mapboxgl.accessToken = "your-token-here";
   ```

## How to Use

1. **Place Start Point**: Click the "Start Point" button, then click on the map to place a red rectangle marker
2. **Continue Fairway**: After placing start point, click "Continue Fairway" to enable end point placement
3. **Place End Point**: Click "End Point" button, then click on the map to place a golf flag marker
4. **Automatic Fairway**: A blue line automatically connects start and end points
5. **Save Fairway**: Click "Save Fairway" to permanently save the current fairway
6. **Create Walking Path**: Click "CREATE WALKING PATH!" then click on the map to create an orange dashed line from the end point
7. **Undo**: Remove the last placed element (walking path, end point, or start point)

## Project Structure

```
src/
├── components/
│   ├── Header.jsx          # App header with title and icons
│   └── MapView.jsx         # Main map component with controls
├── App.jsx                 # Main app component
├── main.jsx               # React entry point
└── index.css              # Global styles
```

## Technologies Used

- **React 19** - UI framework
- **Vite** - Build tool and dev server
- **Mapbox GL JS** - Interactive maps
- **Modern CSS** - Responsive styling

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## License

This project is open source and available under the MIT License.
