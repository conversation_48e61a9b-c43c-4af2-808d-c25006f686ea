import { useState, useEffect } from 'react'

const StatusBar = ({ message, type = 'info', duration = 3000 }) => {
  const [visible, setVisible] = useState(false)
  const [currentMessage, setCurrentMessage] = useState('')
  const [currentType, setCurrentType] = useState('info')

  useEffect(() => {
    if (message) {
      setCurrentMessage(message)
      setCurrentType(type)
      setVisible(true)
      
      const timer = setTimeout(() => {
        setVisible(false)
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [message, type, duration])

  if (!visible || !currentMessage) {
    return null
  }

  const getStatusClass = () => {
    switch (currentType) {
      case 'success':
        return 'status-bar success'
      case 'error':
        return 'status-bar error'
      case 'warning':
        return 'status-bar warning'
      default:
        return 'status-bar'
    }
  }

  const getIcon = () => {
    switch (currentType) {
      case 'success':
        return '✅'
      case 'error':
        return '❌'
      case 'warning':
        return '⚠️'
      default:
        return 'ℹ️'
    }
  }

  return (
    <div className={getStatusClass()}>
      <span style={{ marginRight: '0.5rem' }}>{getIcon()}</span>
      {currentMessage}
    </div>
  )
}

export default StatusBar
