/*!
@fileoverview gl-matrix - High performance matrix and vector operations
<AUTHOR>
<AUTHOR>
@version 3.4.4

Copyright (c) 2015-2025, <PERSON>, <PERSON>.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

*/
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).glMatrix={})}(this,(function(t){"use strict";var n=1e-6,a="undefined"!=typeof Float32Array?Float32Array:Array,r=Math.random;function u(t){return t>=0?Math.round(t):t%.5==0?Math.floor(t):Math.round(t)}var e=Math.PI/180,o=180/Math.PI;var i=Object.freeze({__proto__:null,EPSILON:n,get ARRAY_TYPE(){return a},RANDOM:r,ANGLE_ORDER:"zyx",round:u,setMatrixArrayType:function(t){a=t},toRadian:function(t){return t*e},toDegree:function(t){return t*o},equals:function(t,a){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:n;return Math.abs(t-a)<=r*Math.max(1,Math.abs(t),Math.abs(a))}});function s(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=a[0],s=a[1],c=a[2],h=a[3];return t[0]=r*i+e*s,t[1]=u*i+o*s,t[2]=r*c+e*h,t[3]=u*c+o*h,t}function c(t,n,a){return t[0]=n[0]-a[0],t[1]=n[1]-a[1],t[2]=n[2]-a[2],t[3]=n[3]-a[3],t}var h=s,M=c,f=Object.freeze({__proto__:null,create:function(){var t=new a(4);return a!=Float32Array&&(t[1]=0,t[2]=0),t[0]=1,t[3]=1,t},clone:function(t){var n=new a(4);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n},copy:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t},fromValues:function(t,n,r,u){var e=new a(4);return e[0]=t,e[1]=n,e[2]=r,e[3]=u,e},set:function(t,n,a,r,u){return t[0]=n,t[1]=a,t[2]=r,t[3]=u,t},transpose:function(t,n){if(t===n){var a=n[1];t[1]=n[2],t[2]=a}else t[0]=n[0],t[1]=n[2],t[2]=n[1],t[3]=n[3];return t},invert:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=a*e-u*r;return o?(o=1/o,t[0]=e*o,t[1]=-r*o,t[2]=-u*o,t[3]=a*o,t):null},adjoint:function(t,n){var a=n[0];return t[0]=n[3],t[1]=-n[1],t[2]=-n[2],t[3]=a,t},determinant:function(t){return t[0]*t[3]-t[2]*t[1]},multiply:s,rotate:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=Math.sin(a),s=Math.cos(a);return t[0]=r*s+e*i,t[1]=u*s+o*i,t[2]=r*-i+e*s,t[3]=u*-i+o*s,t},scale:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=a[0],s=a[1];return t[0]=r*i,t[1]=u*i,t[2]=e*s,t[3]=o*s,t},fromRotation:function(t,n){var a=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=a,t[2]=-a,t[3]=r,t},fromScaling:function(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=n[1],t},str:function(t){return"mat2("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"},frob:function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]+t[3]*t[3])},LDU:function(t,n,a,r){return t[2]=r[2]/r[0],a[0]=r[0],a[1]=r[1],a[3]=r[3]-t[2]*a[1],[t,n,a]},add:function(t,n,a){return t[0]=n[0]+a[0],t[1]=n[1]+a[1],t[2]=n[2]+a[2],t[3]=n[3]+a[3],t},subtract:c,exactEquals:function(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]},equals:function(t,a){var r=t[0],u=t[1],e=t[2],o=t[3],i=a[0],s=a[1],c=a[2],h=a[3];return Math.abs(r-i)<=n*Math.max(1,Math.abs(r),Math.abs(i))&&Math.abs(u-s)<=n*Math.max(1,Math.abs(u),Math.abs(s))&&Math.abs(e-c)<=n*Math.max(1,Math.abs(e),Math.abs(c))&&Math.abs(o-h)<=n*Math.max(1,Math.abs(o),Math.abs(h))},multiplyScalar:function(t,n,a){return t[0]=n[0]*a,t[1]=n[1]*a,t[2]=n[2]*a,t[3]=n[3]*a,t},multiplyScalarAndAdd:function(t,n,a,r){return t[0]=n[0]+a[0]*r,t[1]=n[1]+a[1]*r,t[2]=n[2]+a[2]*r,t[3]=n[3]+a[3]*r,t},mul:h,sub:M});function l(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=n[4],s=n[5],c=a[0],h=a[1],M=a[2],f=a[3],l=a[4],v=a[5];return t[0]=r*c+e*h,t[1]=u*c+o*h,t[2]=r*M+e*f,t[3]=u*M+o*f,t[4]=r*l+e*v+i,t[5]=u*l+o*v+s,t}function v(t,n,a){return t[0]=n[0]-a[0],t[1]=n[1]-a[1],t[2]=n[2]-a[2],t[3]=n[3]-a[3],t[4]=n[4]-a[4],t[5]=n[5]-a[5],t}var b=l,m=v,d=Object.freeze({__proto__:null,create:function(){var t=new a(6);return a!=Float32Array&&(t[1]=0,t[2]=0,t[4]=0,t[5]=0),t[0]=1,t[3]=1,t},clone:function(t){var n=new a(6);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n},copy:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},fromValues:function(t,n,r,u,e,o){var i=new a(6);return i[0]=t,i[1]=n,i[2]=r,i[3]=u,i[4]=e,i[5]=o,i},set:function(t,n,a,r,u,e,o){return t[0]=n,t[1]=a,t[2]=r,t[3]=u,t[4]=e,t[5]=o,t},invert:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=n[4],i=n[5],s=a*e-r*u;return s?(s=1/s,t[0]=e*s,t[1]=-r*s,t[2]=-u*s,t[3]=a*s,t[4]=(u*i-e*o)*s,t[5]=(r*o-a*i)*s,t):null},determinant:function(t){return t[0]*t[3]-t[1]*t[2]},multiply:l,rotate:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=n[4],s=n[5],c=Math.sin(a),h=Math.cos(a);return t[0]=r*h+e*c,t[1]=u*h+o*c,t[2]=r*-c+e*h,t[3]=u*-c+o*h,t[4]=i,t[5]=s,t},scale:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=n[4],s=n[5],c=a[0],h=a[1];return t[0]=r*c,t[1]=u*c,t[2]=e*h,t[3]=o*h,t[4]=i,t[5]=s,t},translate:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=n[4],s=n[5],c=a[0],h=a[1];return t[0]=r,t[1]=u,t[2]=e,t[3]=o,t[4]=r*c+e*h+i,t[5]=u*c+o*h+s,t},fromRotation:function(t,n){var a=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=a,t[2]=-a,t[3]=r,t[4]=0,t[5]=0,t},fromScaling:function(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=n[1],t[4]=0,t[5]=0,t},fromTranslation:function(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=n[0],t[5]=n[1],t},str:function(t){return"mat2d("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+")"},frob:function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]+t[3]*t[3]+t[4]*t[4]+t[5]*t[5]+1)},add:function(t,n,a){return t[0]=n[0]+a[0],t[1]=n[1]+a[1],t[2]=n[2]+a[2],t[3]=n[3]+a[3],t[4]=n[4]+a[4],t[5]=n[5]+a[5],t},subtract:v,multiplyScalar:function(t,n,a){return t[0]=n[0]*a,t[1]=n[1]*a,t[2]=n[2]*a,t[3]=n[3]*a,t[4]=n[4]*a,t[5]=n[5]*a,t},multiplyScalarAndAdd:function(t,n,a,r){return t[0]=n[0]+a[0]*r,t[1]=n[1]+a[1]*r,t[2]=n[2]+a[2]*r,t[3]=n[3]+a[3]*r,t[4]=n[4]+a[4]*r,t[5]=n[5]+a[5]*r,t},exactEquals:function(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]},equals:function(t,a){var r=t[0],u=t[1],e=t[2],o=t[3],i=t[4],s=t[5],c=a[0],h=a[1],M=a[2],f=a[3],l=a[4],v=a[5];return Math.abs(r-c)<=n*Math.max(1,Math.abs(r),Math.abs(c))&&Math.abs(u-h)<=n*Math.max(1,Math.abs(u),Math.abs(h))&&Math.abs(e-M)<=n*Math.max(1,Math.abs(e),Math.abs(M))&&Math.abs(o-f)<=n*Math.max(1,Math.abs(o),Math.abs(f))&&Math.abs(i-l)<=n*Math.max(1,Math.abs(i),Math.abs(l))&&Math.abs(s-v)<=n*Math.max(1,Math.abs(s),Math.abs(v))},mul:b,sub:m});function x(){var t=new a(9);return a!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}function q(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=n[4],s=n[5],c=n[6],h=n[7],M=n[8],f=a[0],l=a[1],v=a[2],b=a[3],m=a[4],d=a[5],x=a[6],q=a[7],p=a[8];return t[0]=f*r+l*o+v*c,t[1]=f*u+l*i+v*h,t[2]=f*e+l*s+v*M,t[3]=b*r+m*o+d*c,t[4]=b*u+m*i+d*h,t[5]=b*e+m*s+d*M,t[6]=x*r+q*o+p*c,t[7]=x*u+q*i+p*h,t[8]=x*e+q*s+p*M,t}function p(t,n,a){return t[0]=n[0]-a[0],t[1]=n[1]-a[1],t[2]=n[2]-a[2],t[3]=n[3]-a[3],t[4]=n[4]-a[4],t[5]=n[5]-a[5],t[6]=n[6]-a[6],t[7]=n[7]-a[7],t[8]=n[8]-a[8],t}var y=q,g=p,_=Object.freeze({__proto__:null,create:x,fromMat4:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[4],t[4]=n[5],t[5]=n[6],t[6]=n[8],t[7]=n[9],t[8]=n[10],t},clone:function(t){var n=new a(9);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n},copy:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t},fromValues:function(t,n,r,u,e,o,i,s,c){var h=new a(9);return h[0]=t,h[1]=n,h[2]=r,h[3]=u,h[4]=e,h[5]=o,h[6]=i,h[7]=s,h[8]=c,h},set:function(t,n,a,r,u,e,o,i,s,c){return t[0]=n,t[1]=a,t[2]=r,t[3]=u,t[4]=e,t[5]=o,t[6]=i,t[7]=s,t[8]=c,t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},transpose:function(t,n){if(t===n){var a=n[1],r=n[2],u=n[5];t[1]=n[3],t[2]=n[6],t[3]=a,t[5]=n[7],t[6]=r,t[7]=u}else t[0]=n[0],t[1]=n[3],t[2]=n[6],t[3]=n[1],t[4]=n[4],t[5]=n[7],t[6]=n[2],t[7]=n[5],t[8]=n[8];return t},invert:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=n[4],i=n[5],s=n[6],c=n[7],h=n[8],M=h*o-i*c,f=-h*e+i*s,l=c*e-o*s,v=a*M+r*f+u*l;return v?(v=1/v,t[0]=M*v,t[1]=(-h*r+u*c)*v,t[2]=(i*r-u*o)*v,t[3]=f*v,t[4]=(h*a-u*s)*v,t[5]=(-i*a+u*e)*v,t[6]=l*v,t[7]=(-c*a+r*s)*v,t[8]=(o*a-r*e)*v,t):null},adjoint:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=n[4],i=n[5],s=n[6],c=n[7],h=n[8];return t[0]=o*h-i*c,t[1]=u*c-r*h,t[2]=r*i-u*o,t[3]=i*s-e*h,t[4]=a*h-u*s,t[5]=u*e-a*i,t[6]=e*c-o*s,t[7]=r*s-a*c,t[8]=a*o-r*e,t},determinant:function(t){var n=t[0],a=t[1],r=t[2],u=t[3],e=t[4],o=t[5],i=t[6],s=t[7],c=t[8];return n*(c*e-o*s)+a*(-c*u+o*i)+r*(s*u-e*i)},multiply:q,translate:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=n[4],s=n[5],c=n[6],h=n[7],M=n[8],f=a[0],l=a[1];return t[0]=r,t[1]=u,t[2]=e,t[3]=o,t[4]=i,t[5]=s,t[6]=f*r+l*o+c,t[7]=f*u+l*i+h,t[8]=f*e+l*s+M,t},rotate:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=n[4],s=n[5],c=n[6],h=n[7],M=n[8],f=Math.sin(a),l=Math.cos(a);return t[0]=l*r+f*o,t[1]=l*u+f*i,t[2]=l*e+f*s,t[3]=l*o-f*r,t[4]=l*i-f*u,t[5]=l*s-f*e,t[6]=c,t[7]=h,t[8]=M,t},scale:function(t,n,a){var r=a[0],u=a[1];return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=u*n[3],t[4]=u*n[4],t[5]=u*n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t},fromTranslation:function(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=n[0],t[7]=n[1],t[8]=1,t},fromRotation:function(t,n){var a=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=a,t[2]=0,t[3]=-a,t[4]=r,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},fromScaling:function(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=0,t[4]=n[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},fromMat2d:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=0,t[3]=n[2],t[4]=n[3],t[5]=0,t[6]=n[4],t[7]=n[5],t[8]=1,t},fromQuat:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=a+a,i=r+r,s=u+u,c=a*o,h=r*o,M=r*i,f=u*o,l=u*i,v=u*s,b=e*o,m=e*i,d=e*s;return t[0]=1-M-v,t[3]=h-d,t[6]=f+m,t[1]=h+d,t[4]=1-c-v,t[7]=l-b,t[2]=f-m,t[5]=l+b,t[8]=1-c-M,t},normalFromMat4:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=n[4],i=n[5],s=n[6],c=n[7],h=n[8],M=n[9],f=n[10],l=n[11],v=n[12],b=n[13],m=n[14],d=n[15],x=a*i-r*o,q=a*s-u*o,p=a*c-e*o,y=r*s-u*i,g=r*c-e*i,_=u*c-e*s,A=h*b-M*v,w=h*m-f*v,z=h*d-l*v,R=M*m-f*b,O=M*d-l*b,j=f*d-l*m,E=x*j-q*O+p*R+y*z-g*w+_*A;return E?(E=1/E,t[0]=(i*j-s*O+c*R)*E,t[1]=(s*z-o*j-c*w)*E,t[2]=(o*O-i*z+c*A)*E,t[3]=(u*O-r*j-e*R)*E,t[4]=(a*j-u*z+e*w)*E,t[5]=(r*z-a*O-e*A)*E,t[6]=(b*_-m*g+d*y)*E,t[7]=(m*p-v*_-d*q)*E,t[8]=(v*g-b*p+d*x)*E,t):null},projection:function(t,n,a){return t[0]=2/n,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/a,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t},str:function(t){return"mat3("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+")"},frob:function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]+t[3]*t[3]+t[4]*t[4]+t[5]*t[5]+t[6]*t[6]+t[7]*t[7]+t[8]*t[8])},add:function(t,n,a){return t[0]=n[0]+a[0],t[1]=n[1]+a[1],t[2]=n[2]+a[2],t[3]=n[3]+a[3],t[4]=n[4]+a[4],t[5]=n[5]+a[5],t[6]=n[6]+a[6],t[7]=n[7]+a[7],t[8]=n[8]+a[8],t},subtract:p,multiplyScalar:function(t,n,a){return t[0]=n[0]*a,t[1]=n[1]*a,t[2]=n[2]*a,t[3]=n[3]*a,t[4]=n[4]*a,t[5]=n[5]*a,t[6]=n[6]*a,t[7]=n[7]*a,t[8]=n[8]*a,t},multiplyScalarAndAdd:function(t,n,a,r){return t[0]=n[0]+a[0]*r,t[1]=n[1]+a[1]*r,t[2]=n[2]+a[2]*r,t[3]=n[3]+a[3]*r,t[4]=n[4]+a[4]*r,t[5]=n[5]+a[5]*r,t[6]=n[6]+a[6]*r,t[7]=n[7]+a[7]*r,t[8]=n[8]+a[8]*r,t},exactEquals:function(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]},equals:function(t,a){var r=t[0],u=t[1],e=t[2],o=t[3],i=t[4],s=t[5],c=t[6],h=t[7],M=t[8],f=a[0],l=a[1],v=a[2],b=a[3],m=a[4],d=a[5],x=a[6],q=a[7],p=a[8];return Math.abs(r-f)<=n*Math.max(1,Math.abs(r),Math.abs(f))&&Math.abs(u-l)<=n*Math.max(1,Math.abs(u),Math.abs(l))&&Math.abs(e-v)<=n*Math.max(1,Math.abs(e),Math.abs(v))&&Math.abs(o-b)<=n*Math.max(1,Math.abs(o),Math.abs(b))&&Math.abs(i-m)<=n*Math.max(1,Math.abs(i),Math.abs(m))&&Math.abs(s-d)<=n*Math.max(1,Math.abs(s),Math.abs(d))&&Math.abs(c-x)<=n*Math.max(1,Math.abs(c),Math.abs(x))&&Math.abs(h-q)<=n*Math.max(1,Math.abs(h),Math.abs(q))&&Math.abs(M-p)<=n*Math.max(1,Math.abs(M),Math.abs(p))},mul:y,sub:g});function A(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function w(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=n[4],s=n[5],c=n[6],h=n[7],M=n[8],f=n[9],l=n[10],v=n[11],b=n[12],m=n[13],d=n[14],x=n[15],q=a[0],p=a[1],y=a[2],g=a[3];return t[0]=q*r+p*i+y*M+g*b,t[1]=q*u+p*s+y*f+g*m,t[2]=q*e+p*c+y*l+g*d,t[3]=q*o+p*h+y*v+g*x,q=a[4],p=a[5],y=a[6],g=a[7],t[4]=q*r+p*i+y*M+g*b,t[5]=q*u+p*s+y*f+g*m,t[6]=q*e+p*c+y*l+g*d,t[7]=q*o+p*h+y*v+g*x,q=a[8],p=a[9],y=a[10],g=a[11],t[8]=q*r+p*i+y*M+g*b,t[9]=q*u+p*s+y*f+g*m,t[10]=q*e+p*c+y*l+g*d,t[11]=q*o+p*h+y*v+g*x,q=a[12],p=a[13],y=a[14],g=a[15],t[12]=q*r+p*i+y*M+g*b,t[13]=q*u+p*s+y*f+g*m,t[14]=q*e+p*c+y*l+g*d,t[15]=q*o+p*h+y*v+g*x,t}function z(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=r+r,s=u+u,c=e+e,h=r*i,M=r*s,f=r*c,l=u*s,v=u*c,b=e*c,m=o*i,d=o*s,x=o*c;return t[0]=1-(l+b),t[1]=M+x,t[2]=f-d,t[3]=0,t[4]=M-x,t[5]=1-(h+b),t[6]=v+m,t[7]=0,t[8]=f+d,t[9]=v-m,t[10]=1-(h+l),t[11]=0,t[12]=a[0],t[13]=a[1],t[14]=a[2],t[15]=1,t}function R(t,n){return t[0]=n[12],t[1]=n[13],t[2]=n[14],t}function O(t,n){var a=n[0],r=n[1],u=n[2],e=n[4],o=n[5],i=n[6],s=n[8],c=n[9],h=n[10];return t[0]=Math.sqrt(a*a+r*r+u*u),t[1]=Math.sqrt(e*e+o*o+i*i),t[2]=Math.sqrt(s*s+c*c+h*h),t}function j(t,n){var r=new a(3);O(r,n);var u=1/r[0],e=1/r[1],o=1/r[2],i=n[0]*u,s=n[1]*e,c=n[2]*o,h=n[4]*u,M=n[5]*e,f=n[6]*o,l=n[8]*u,v=n[9]*e,b=n[10]*o,m=i+M+b,d=0;return m>0?(d=2*Math.sqrt(m+1),t[3]=.25*d,t[0]=(f-v)/d,t[1]=(l-c)/d,t[2]=(s-h)/d):i>M&&i>b?(d=2*Math.sqrt(1+i-M-b),t[3]=(f-v)/d,t[0]=.25*d,t[1]=(s+h)/d,t[2]=(l+c)/d):M>b?(d=2*Math.sqrt(1+M-i-b),t[3]=(l-c)/d,t[0]=(s+h)/d,t[1]=.25*d,t[2]=(f+v)/d):(d=2*Math.sqrt(1+b-i-M),t[3]=(s-h)/d,t[0]=(l+c)/d,t[1]=(f+v)/d,t[2]=.25*d),t}function E(t,n,a,r,u){var e=1/Math.tan(n/2);if(t[0]=e/a,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=u&&u!==1/0){var o=1/(r-u);t[10]=(u+r)*o,t[14]=2*u*r*o}else t[10]=-1,t[14]=-2*r;return t}var P=E;function T(t,n,a,r,u,e,o){var i=1/(n-a),s=1/(r-u),c=1/(e-o);return t[0]=-2*i,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*s,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*c,t[11]=0,t[12]=(n+a)*i,t[13]=(u+r)*s,t[14]=(o+e)*c,t[15]=1,t}var D=T;function S(t,n,a){return t[0]=n[0]-a[0],t[1]=n[1]-a[1],t[2]=n[2]-a[2],t[3]=n[3]-a[3],t[4]=n[4]-a[4],t[5]=n[5]-a[5],t[6]=n[6]-a[6],t[7]=n[7]-a[7],t[8]=n[8]-a[8],t[9]=n[9]-a[9],t[10]=n[10]-a[10],t[11]=n[11]-a[11],t[12]=n[12]-a[12],t[13]=n[13]-a[13],t[14]=n[14]-a[14],t[15]=n[15]-a[15],t}var I=w,F=S,L=Object.freeze({__proto__:null,create:function(){var t=new a(16);return a!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=0,t[12]=0,t[13]=0,t[14]=0),t[0]=1,t[5]=1,t[10]=1,t[15]=1,t},clone:function(t){var n=new a(16);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n[9]=t[9],n[10]=t[10],n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n},copy:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t[9]=n[9],t[10]=n[10],t[11]=n[11],t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15],t},fromValues:function(t,n,r,u,e,o,i,s,c,h,M,f,l,v,b,m){var d=new a(16);return d[0]=t,d[1]=n,d[2]=r,d[3]=u,d[4]=e,d[5]=o,d[6]=i,d[7]=s,d[8]=c,d[9]=h,d[10]=M,d[11]=f,d[12]=l,d[13]=v,d[14]=b,d[15]=m,d},set:function(t,n,a,r,u,e,o,i,s,c,h,M,f,l,v,b,m){return t[0]=n,t[1]=a,t[2]=r,t[3]=u,t[4]=e,t[5]=o,t[6]=i,t[7]=s,t[8]=c,t[9]=h,t[10]=M,t[11]=f,t[12]=l,t[13]=v,t[14]=b,t[15]=m,t},identity:A,transpose:function(t,n){if(t===n){var a=n[1],r=n[2],u=n[3],e=n[6],o=n[7],i=n[11];t[1]=n[4],t[2]=n[8],t[3]=n[12],t[4]=a,t[6]=n[9],t[7]=n[13],t[8]=r,t[9]=e,t[11]=n[14],t[12]=u,t[13]=o,t[14]=i}else t[0]=n[0],t[1]=n[4],t[2]=n[8],t[3]=n[12],t[4]=n[1],t[5]=n[5],t[6]=n[9],t[7]=n[13],t[8]=n[2],t[9]=n[6],t[10]=n[10],t[11]=n[14],t[12]=n[3],t[13]=n[7],t[14]=n[11],t[15]=n[15];return t},invert:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=n[4],i=n[5],s=n[6],c=n[7],h=n[8],M=n[9],f=n[10],l=n[11],v=n[12],b=n[13],m=n[14],d=n[15],x=a*i-r*o,q=a*s-u*o,p=a*c-e*o,y=r*s-u*i,g=r*c-e*i,_=u*c-e*s,A=h*b-M*v,w=h*m-f*v,z=h*d-l*v,R=M*m-f*b,O=M*d-l*b,j=f*d-l*m,E=x*j-q*O+p*R+y*z-g*w+_*A;return E?(E=1/E,t[0]=(i*j-s*O+c*R)*E,t[1]=(u*O-r*j-e*R)*E,t[2]=(b*_-m*g+d*y)*E,t[3]=(f*g-M*_-l*y)*E,t[4]=(s*z-o*j-c*w)*E,t[5]=(a*j-u*z+e*w)*E,t[6]=(m*p-v*_-d*q)*E,t[7]=(h*_-f*p+l*q)*E,t[8]=(o*O-i*z+c*A)*E,t[9]=(r*z-a*O-e*A)*E,t[10]=(v*g-b*p+d*x)*E,t[11]=(M*p-h*g-l*x)*E,t[12]=(i*w-o*R-s*A)*E,t[13]=(a*R-r*w+u*A)*E,t[14]=(b*q-v*y-m*x)*E,t[15]=(h*y-M*q+f*x)*E,t):null},adjoint:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=n[4],i=n[5],s=n[6],c=n[7],h=n[8],M=n[9],f=n[10],l=n[11],v=n[12],b=n[13],m=n[14],d=n[15],x=a*i-r*o,q=a*s-u*o,p=a*c-e*o,y=r*s-u*i,g=r*c-e*i,_=u*c-e*s,A=h*b-M*v,w=h*m-f*v,z=h*d-l*v,R=M*m-f*b,O=M*d-l*b,j=f*d-l*m;return t[0]=i*j-s*O+c*R,t[1]=u*O-r*j-e*R,t[2]=b*_-m*g+d*y,t[3]=f*g-M*_-l*y,t[4]=s*z-o*j-c*w,t[5]=a*j-u*z+e*w,t[6]=m*p-v*_-d*q,t[7]=h*_-f*p+l*q,t[8]=o*O-i*z+c*A,t[9]=r*z-a*O-e*A,t[10]=v*g-b*p+d*x,t[11]=M*p-h*g-l*x,t[12]=i*w-o*R-s*A,t[13]=a*R-r*w+u*A,t[14]=b*q-v*y-m*x,t[15]=h*y-M*q+f*x,t},determinant:function(t){var n=t[0],a=t[1],r=t[2],u=t[3],e=t[4],o=t[5],i=t[6],s=t[7],c=t[8],h=t[9],M=t[10],f=t[11],l=t[12],v=t[13],b=t[14],m=n*o-a*e,d=n*i-r*e,x=a*i-r*o,q=c*v-h*l,p=c*b-M*l,y=h*b-M*v;return s*(n*y-a*p+r*q)-u*(e*y-o*p+i*q)+t[15]*(c*x-h*d+M*m)-f*(l*x-v*d+b*m)},multiply:w,translate:function(t,n,a){var r,u,e,o,i,s,c,h,M,f,l,v,b=a[0],m=a[1],d=a[2];return n===t?(t[12]=n[0]*b+n[4]*m+n[8]*d+n[12],t[13]=n[1]*b+n[5]*m+n[9]*d+n[13],t[14]=n[2]*b+n[6]*m+n[10]*d+n[14],t[15]=n[3]*b+n[7]*m+n[11]*d+n[15]):(r=n[0],u=n[1],e=n[2],o=n[3],i=n[4],s=n[5],c=n[6],h=n[7],M=n[8],f=n[9],l=n[10],v=n[11],t[0]=r,t[1]=u,t[2]=e,t[3]=o,t[4]=i,t[5]=s,t[6]=c,t[7]=h,t[8]=M,t[9]=f,t[10]=l,t[11]=v,t[12]=r*b+i*m+M*d+n[12],t[13]=u*b+s*m+f*d+n[13],t[14]=e*b+c*m+l*d+n[14],t[15]=o*b+h*m+v*d+n[15]),t},scale:function(t,n,a){var r=a[0],u=a[1],e=a[2];return t[0]=n[0]*r,t[1]=n[1]*r,t[2]=n[2]*r,t[3]=n[3]*r,t[4]=n[4]*u,t[5]=n[5]*u,t[6]=n[6]*u,t[7]=n[7]*u,t[8]=n[8]*e,t[9]=n[9]*e,t[10]=n[10]*e,t[11]=n[11]*e,t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15],t},rotate:function(t,a,r,u){var e,o,i,s,c,h,M,f,l,v,b,m,d,x,q,p,y,g,_,A,w,z,R,O,j=u[0],E=u[1],P=u[2],T=Math.sqrt(j*j+E*E+P*P);return T<n?null:(j*=T=1/T,E*=T,P*=T,e=Math.sin(r),i=1-(o=Math.cos(r)),s=a[0],c=a[1],h=a[2],M=a[3],f=a[4],l=a[5],v=a[6],b=a[7],m=a[8],d=a[9],x=a[10],q=a[11],p=j*j*i+o,y=E*j*i+P*e,g=P*j*i-E*e,_=j*E*i-P*e,A=E*E*i+o,w=P*E*i+j*e,z=j*P*i+E*e,R=E*P*i-j*e,O=P*P*i+o,t[0]=s*p+f*y+m*g,t[1]=c*p+l*y+d*g,t[2]=h*p+v*y+x*g,t[3]=M*p+b*y+q*g,t[4]=s*_+f*A+m*w,t[5]=c*_+l*A+d*w,t[6]=h*_+v*A+x*w,t[7]=M*_+b*A+q*w,t[8]=s*z+f*R+m*O,t[9]=c*z+l*R+d*O,t[10]=h*z+v*R+x*O,t[11]=M*z+b*R+q*O,a!==t&&(t[12]=a[12],t[13]=a[13],t[14]=a[14],t[15]=a[15]),t)},rotateX:function(t,n,a){var r=Math.sin(a),u=Math.cos(a),e=n[4],o=n[5],i=n[6],s=n[7],c=n[8],h=n[9],M=n[10],f=n[11];return n!==t&&(t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15]),t[4]=e*u+c*r,t[5]=o*u+h*r,t[6]=i*u+M*r,t[7]=s*u+f*r,t[8]=c*u-e*r,t[9]=h*u-o*r,t[10]=M*u-i*r,t[11]=f*u-s*r,t},rotateY:function(t,n,a){var r=Math.sin(a),u=Math.cos(a),e=n[0],o=n[1],i=n[2],s=n[3],c=n[8],h=n[9],M=n[10],f=n[11];return n!==t&&(t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15]),t[0]=e*u-c*r,t[1]=o*u-h*r,t[2]=i*u-M*r,t[3]=s*u-f*r,t[8]=e*r+c*u,t[9]=o*r+h*u,t[10]=i*r+M*u,t[11]=s*r+f*u,t},rotateZ:function(t,n,a){var r=Math.sin(a),u=Math.cos(a),e=n[0],o=n[1],i=n[2],s=n[3],c=n[4],h=n[5],M=n[6],f=n[7];return n!==t&&(t[8]=n[8],t[9]=n[9],t[10]=n[10],t[11]=n[11],t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15]),t[0]=e*u+c*r,t[1]=o*u+h*r,t[2]=i*u+M*r,t[3]=s*u+f*r,t[4]=c*u-e*r,t[5]=h*u-o*r,t[6]=M*u-i*r,t[7]=f*u-s*r,t},fromTranslation:function(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=n[0],t[13]=n[1],t[14]=n[2],t[15]=1,t},fromScaling:function(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=n[1],t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=n[2],t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromRotation:function(t,a,r){var u,e,o,i=r[0],s=r[1],c=r[2],h=Math.sqrt(i*i+s*s+c*c);return h<n?null:(i*=h=1/h,s*=h,c*=h,u=Math.sin(a),o=1-(e=Math.cos(a)),t[0]=i*i*o+e,t[1]=s*i*o+c*u,t[2]=c*i*o-s*u,t[3]=0,t[4]=i*s*o-c*u,t[5]=s*s*o+e,t[6]=c*s*o+i*u,t[7]=0,t[8]=i*c*o+s*u,t[9]=s*c*o-i*u,t[10]=c*c*o+e,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t)},fromXRotation:function(t,n){var a=Math.sin(n),r=Math.cos(n);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=r,t[6]=a,t[7]=0,t[8]=0,t[9]=-a,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromYRotation:function(t,n){var a=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=0,t[2]=-a,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=a,t[9]=0,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromZRotation:function(t,n){var a=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=a,t[2]=0,t[3]=0,t[4]=-a,t[5]=r,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromRotationTranslation:z,fromQuat2:function(t,n){var r=new a(3),u=-n[0],e=-n[1],o=-n[2],i=n[3],s=n[4],c=n[5],h=n[6],M=n[7],f=u*u+e*e+o*o+i*i;return f>0?(r[0]=2*(s*i+M*u+c*o-h*e)/f,r[1]=2*(c*i+M*e+h*u-s*o)/f,r[2]=2*(h*i+M*o+s*e-c*u)/f):(r[0]=2*(s*i+M*u+c*o-h*e),r[1]=2*(c*i+M*e+h*u-s*o),r[2]=2*(h*i+M*o+s*e-c*u)),z(t,n,r),t},getTranslation:R,getScaling:O,getRotation:j,decompose:function(t,n,a,r){n[0]=r[12],n[1]=r[13],n[2]=r[14];var u=r[0],e=r[1],o=r[2],i=r[4],s=r[5],c=r[6],h=r[8],M=r[9],f=r[10];a[0]=Math.sqrt(u*u+e*e+o*o),a[1]=Math.sqrt(i*i+s*s+c*c),a[2]=Math.sqrt(h*h+M*M+f*f);var l=1/a[0],v=1/a[1],b=1/a[2],m=u*l,d=e*v,x=o*b,q=i*l,p=s*v,y=c*b,g=h*l,_=M*v,A=f*b,w=m+p+A,z=0;return w>0?(z=2*Math.sqrt(w+1),t[3]=.25*z,t[0]=(y-_)/z,t[1]=(g-x)/z,t[2]=(d-q)/z):m>p&&m>A?(z=2*Math.sqrt(1+m-p-A),t[3]=(y-_)/z,t[0]=.25*z,t[1]=(d+q)/z,t[2]=(g+x)/z):p>A?(z=2*Math.sqrt(1+p-m-A),t[3]=(g-x)/z,t[0]=(d+q)/z,t[1]=.25*z,t[2]=(y+_)/z):(z=2*Math.sqrt(1+A-m-p),t[3]=(d-q)/z,t[0]=(g+x)/z,t[1]=(y+_)/z,t[2]=.25*z),t},fromRotationTranslationScale:function(t,n,a,r){var u=n[0],e=n[1],o=n[2],i=n[3],s=u+u,c=e+e,h=o+o,M=u*s,f=u*c,l=u*h,v=e*c,b=e*h,m=o*h,d=i*s,x=i*c,q=i*h,p=r[0],y=r[1],g=r[2];return t[0]=(1-(v+m))*p,t[1]=(f+q)*p,t[2]=(l-x)*p,t[3]=0,t[4]=(f-q)*y,t[5]=(1-(M+m))*y,t[6]=(b+d)*y,t[7]=0,t[8]=(l+x)*g,t[9]=(b-d)*g,t[10]=(1-(M+v))*g,t[11]=0,t[12]=a[0],t[13]=a[1],t[14]=a[2],t[15]=1,t},fromRotationTranslationScaleOrigin:function(t,n,a,r,u){var e=n[0],o=n[1],i=n[2],s=n[3],c=e+e,h=o+o,M=i+i,f=e*c,l=e*h,v=e*M,b=o*h,m=o*M,d=i*M,x=s*c,q=s*h,p=s*M,y=r[0],g=r[1],_=r[2],A=u[0],w=u[1],z=u[2],R=(1-(b+d))*y,O=(l+p)*y,j=(v-q)*y,E=(l-p)*g,P=(1-(f+d))*g,T=(m+x)*g,D=(v+q)*_,S=(m-x)*_,I=(1-(f+b))*_;return t[0]=R,t[1]=O,t[2]=j,t[3]=0,t[4]=E,t[5]=P,t[6]=T,t[7]=0,t[8]=D,t[9]=S,t[10]=I,t[11]=0,t[12]=a[0]+A-(R*A+E*w+D*z),t[13]=a[1]+w-(O*A+P*w+S*z),t[14]=a[2]+z-(j*A+T*w+I*z),t[15]=1,t},fromQuat:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=a+a,i=r+r,s=u+u,c=a*o,h=r*o,M=r*i,f=u*o,l=u*i,v=u*s,b=e*o,m=e*i,d=e*s;return t[0]=1-M-v,t[1]=h+d,t[2]=f-m,t[3]=0,t[4]=h-d,t[5]=1-c-v,t[6]=l+b,t[7]=0,t[8]=f+m,t[9]=l-b,t[10]=1-c-M,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},frustum:function(t,n,a,r,u,e,o){var i=1/(a-n),s=1/(u-r),c=1/(e-o);return t[0]=2*e*i,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=2*e*s,t[6]=0,t[7]=0,t[8]=(a+n)*i,t[9]=(u+r)*s,t[10]=(o+e)*c,t[11]=-1,t[12]=0,t[13]=0,t[14]=o*e*2*c,t[15]=0,t},perspectiveNO:E,perspective:P,perspectiveZO:function(t,n,a,r,u){var e=1/Math.tan(n/2);if(t[0]=e/a,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=u&&u!==1/0){var o=1/(r-u);t[10]=u*o,t[14]=u*r*o}else t[10]=-1,t[14]=-r;return t},perspectiveFromFieldOfView:function(t,n,a,r){var u=Math.tan(n.upDegrees*Math.PI/180),e=Math.tan(n.downDegrees*Math.PI/180),o=Math.tan(n.leftDegrees*Math.PI/180),i=Math.tan(n.rightDegrees*Math.PI/180),s=2/(o+i),c=2/(u+e);return t[0]=s,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=c,t[6]=0,t[7]=0,t[8]=-(o-i)*s*.5,t[9]=(u-e)*c*.5,t[10]=r/(a-r),t[11]=-1,t[12]=0,t[13]=0,t[14]=r*a/(a-r),t[15]=0,t},orthoNO:T,ortho:D,orthoZO:function(t,n,a,r,u,e,o){var i=1/(n-a),s=1/(r-u),c=1/(e-o);return t[0]=-2*i,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*s,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=c,t[11]=0,t[12]=(n+a)*i,t[13]=(u+r)*s,t[14]=e*c,t[15]=1,t},lookAt:function(t,a,r,u){var e,o,i,s,c,h,M,f,l,v,b=a[0],m=a[1],d=a[2],x=u[0],q=u[1],p=u[2],y=r[0],g=r[1],_=r[2];return Math.abs(b-y)<n&&Math.abs(m-g)<n&&Math.abs(d-_)<n?A(t):(M=b-y,f=m-g,l=d-_,e=q*(l*=v=1/Math.sqrt(M*M+f*f+l*l))-p*(f*=v),o=p*(M*=v)-x*l,i=x*f-q*M,(v=Math.sqrt(e*e+o*o+i*i))?(e*=v=1/v,o*=v,i*=v):(e=0,o=0,i=0),s=f*i-l*o,c=l*e-M*i,h=M*o-f*e,(v=Math.sqrt(s*s+c*c+h*h))?(s*=v=1/v,c*=v,h*=v):(s=0,c=0,h=0),t[0]=e,t[1]=s,t[2]=M,t[3]=0,t[4]=o,t[5]=c,t[6]=f,t[7]=0,t[8]=i,t[9]=h,t[10]=l,t[11]=0,t[12]=-(e*b+o*m+i*d),t[13]=-(s*b+c*m+h*d),t[14]=-(M*b+f*m+l*d),t[15]=1,t)},targetTo:function(t,n,a,r){var u=n[0],e=n[1],o=n[2],i=r[0],s=r[1],c=r[2],h=u-a[0],M=e-a[1],f=o-a[2],l=h*h+M*M+f*f;l>0&&(h*=l=1/Math.sqrt(l),M*=l,f*=l);var v=s*f-c*M,b=c*h-i*f,m=i*M-s*h;return(l=v*v+b*b+m*m)>0&&(v*=l=1/Math.sqrt(l),b*=l,m*=l),t[0]=v,t[1]=b,t[2]=m,t[3]=0,t[4]=M*m-f*b,t[5]=f*v-h*m,t[6]=h*b-M*v,t[7]=0,t[8]=h,t[9]=M,t[10]=f,t[11]=0,t[12]=u,t[13]=e,t[14]=o,t[15]=1,t},str:function(t){return"mat4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+", "+t[9]+", "+t[10]+", "+t[11]+", "+t[12]+", "+t[13]+", "+t[14]+", "+t[15]+")"},frob:function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]+t[3]*t[3]+t[4]*t[4]+t[5]*t[5]+t[6]*t[6]+t[7]*t[7]+t[8]*t[8]+t[9]*t[9]+t[10]*t[10]+t[11]*t[11]+t[12]*t[12]+t[13]*t[13]+t[14]*t[14]+t[15]*t[15])},add:function(t,n,a){return t[0]=n[0]+a[0],t[1]=n[1]+a[1],t[2]=n[2]+a[2],t[3]=n[3]+a[3],t[4]=n[4]+a[4],t[5]=n[5]+a[5],t[6]=n[6]+a[6],t[7]=n[7]+a[7],t[8]=n[8]+a[8],t[9]=n[9]+a[9],t[10]=n[10]+a[10],t[11]=n[11]+a[11],t[12]=n[12]+a[12],t[13]=n[13]+a[13],t[14]=n[14]+a[14],t[15]=n[15]+a[15],t},subtract:S,multiplyScalar:function(t,n,a){return t[0]=n[0]*a,t[1]=n[1]*a,t[2]=n[2]*a,t[3]=n[3]*a,t[4]=n[4]*a,t[5]=n[5]*a,t[6]=n[6]*a,t[7]=n[7]*a,t[8]=n[8]*a,t[9]=n[9]*a,t[10]=n[10]*a,t[11]=n[11]*a,t[12]=n[12]*a,t[13]=n[13]*a,t[14]=n[14]*a,t[15]=n[15]*a,t},multiplyScalarAndAdd:function(t,n,a,r){return t[0]=n[0]+a[0]*r,t[1]=n[1]+a[1]*r,t[2]=n[2]+a[2]*r,t[3]=n[3]+a[3]*r,t[4]=n[4]+a[4]*r,t[5]=n[5]+a[5]*r,t[6]=n[6]+a[6]*r,t[7]=n[7]+a[7]*r,t[8]=n[8]+a[8]*r,t[9]=n[9]+a[9]*r,t[10]=n[10]+a[10]*r,t[11]=n[11]+a[11]*r,t[12]=n[12]+a[12]*r,t[13]=n[13]+a[13]*r,t[14]=n[14]+a[14]*r,t[15]=n[15]+a[15]*r,t},exactEquals:function(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]&&t[9]===n[9]&&t[10]===n[10]&&t[11]===n[11]&&t[12]===n[12]&&t[13]===n[13]&&t[14]===n[14]&&t[15]===n[15]},equals:function(t,a){var r=t[0],u=t[1],e=t[2],o=t[3],i=t[4],s=t[5],c=t[6],h=t[7],M=t[8],f=t[9],l=t[10],v=t[11],b=t[12],m=t[13],d=t[14],x=t[15],q=a[0],p=a[1],y=a[2],g=a[3],_=a[4],A=a[5],w=a[6],z=a[7],R=a[8],O=a[9],j=a[10],E=a[11],P=a[12],T=a[13],D=a[14],S=a[15];return Math.abs(r-q)<=n*Math.max(1,Math.abs(r),Math.abs(q))&&Math.abs(u-p)<=n*Math.max(1,Math.abs(u),Math.abs(p))&&Math.abs(e-y)<=n*Math.max(1,Math.abs(e),Math.abs(y))&&Math.abs(o-g)<=n*Math.max(1,Math.abs(o),Math.abs(g))&&Math.abs(i-_)<=n*Math.max(1,Math.abs(i),Math.abs(_))&&Math.abs(s-A)<=n*Math.max(1,Math.abs(s),Math.abs(A))&&Math.abs(c-w)<=n*Math.max(1,Math.abs(c),Math.abs(w))&&Math.abs(h-z)<=n*Math.max(1,Math.abs(h),Math.abs(z))&&Math.abs(M-R)<=n*Math.max(1,Math.abs(M),Math.abs(R))&&Math.abs(f-O)<=n*Math.max(1,Math.abs(f),Math.abs(O))&&Math.abs(l-j)<=n*Math.max(1,Math.abs(l),Math.abs(j))&&Math.abs(v-E)<=n*Math.max(1,Math.abs(v),Math.abs(E))&&Math.abs(b-P)<=n*Math.max(1,Math.abs(b),Math.abs(P))&&Math.abs(m-T)<=n*Math.max(1,Math.abs(m),Math.abs(T))&&Math.abs(d-D)<=n*Math.max(1,Math.abs(d),Math.abs(D))&&Math.abs(x-S)<=n*Math.max(1,Math.abs(x),Math.abs(S))},mul:I,sub:F});function V(){var t=new a(3);return a!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t}function k(t){var n=t[0],a=t[1],r=t[2];return Math.sqrt(n*n+a*a+r*r)}function Q(t,n,r){var u=new a(3);return u[0]=t,u[1]=n,u[2]=r,u}function Y(t,n,a){return t[0]=n[0]-a[0],t[1]=n[1]-a[1],t[2]=n[2]-a[2],t}function Z(t,n,a){return t[0]=n[0]*a[0],t[1]=n[1]*a[1],t[2]=n[2]*a[2],t}function N(t,n,a){return t[0]=n[0]/a[0],t[1]=n[1]/a[1],t[2]=n[2]/a[2],t}function X(t,n){var a=n[0]-t[0],r=n[1]-t[1],u=n[2]-t[2];return Math.sqrt(a*a+r*r+u*u)}function B(t,n){var a=n[0]-t[0],r=n[1]-t[1],u=n[2]-t[2];return a*a+r*r+u*u}function U(t){var n=t[0],a=t[1],r=t[2];return n*n+a*a+r*r}function G(t,n){var a=n[0],r=n[1],u=n[2],e=a*a+r*r+u*u;return e>0&&(e=1/Math.sqrt(e)),t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t}function W(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function C(t,n,a){var r=n[0],u=n[1],e=n[2],o=a[0],i=a[1],s=a[2];return t[0]=u*s-e*i,t[1]=e*o-r*s,t[2]=r*i-u*o,t}var H,J=Y,K=Z,$=N,tt=X,nt=B,at=k,rt=U,ut=(H=V(),function(t,n,a,r,u,e){var o,i;for(n||(n=3),a||(a=0),i=r?Math.min(r*n+a,t.length):t.length,o=a;o<i;o+=n)H[0]=t[o],H[1]=t[o+1],H[2]=t[o+2],u(H,H,e),t[o]=H[0],t[o+1]=H[1],t[o+2]=H[2];return t}),et=Object.freeze({__proto__:null,create:V,clone:function(t){var n=new a(3);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n},length:k,fromValues:Q,copy:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t},set:function(t,n,a,r){return t[0]=n,t[1]=a,t[2]=r,t},add:function(t,n,a){return t[0]=n[0]+a[0],t[1]=n[1]+a[1],t[2]=n[2]+a[2],t},subtract:Y,multiply:Z,divide:N,ceil:function(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t[2]=Math.ceil(n[2]),t},floor:function(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t[2]=Math.floor(n[2]),t},min:function(t,n,a){return t[0]=Math.min(n[0],a[0]),t[1]=Math.min(n[1],a[1]),t[2]=Math.min(n[2],a[2]),t},max:function(t,n,a){return t[0]=Math.max(n[0],a[0]),t[1]=Math.max(n[1],a[1]),t[2]=Math.max(n[2],a[2]),t},round:function(t,n){return t[0]=u(n[0]),t[1]=u(n[1]),t[2]=u(n[2]),t},scale:function(t,n,a){return t[0]=n[0]*a,t[1]=n[1]*a,t[2]=n[2]*a,t},scaleAndAdd:function(t,n,a,r){return t[0]=n[0]+a[0]*r,t[1]=n[1]+a[1]*r,t[2]=n[2]+a[2]*r,t},distance:X,squaredDistance:B,squaredLength:U,negate:function(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t},inverse:function(t,n){return t[0]=1/n[0],t[1]=1/n[1],t[2]=1/n[2],t},normalize:G,dot:W,cross:C,lerp:function(t,n,a,r){var u=n[0],e=n[1],o=n[2];return t[0]=u+r*(a[0]-u),t[1]=e+r*(a[1]-e),t[2]=o+r*(a[2]-o),t},slerp:function(t,n,a,r){var u=Math.acos(Math.min(Math.max(W(n,a),-1),1)),e=Math.sin(u),o=Math.sin((1-r)*u)/e,i=Math.sin(r*u)/e;return t[0]=o*n[0]+i*a[0],t[1]=o*n[1]+i*a[1],t[2]=o*n[2]+i*a[2],t},hermite:function(t,n,a,r,u,e){var o=e*e,i=o*(2*e-3)+1,s=o*(e-2)+e,c=o*(e-1),h=o*(3-2*e);return t[0]=n[0]*i+a[0]*s+r[0]*c+u[0]*h,t[1]=n[1]*i+a[1]*s+r[1]*c+u[1]*h,t[2]=n[2]*i+a[2]*s+r[2]*c+u[2]*h,t},bezier:function(t,n,a,r,u,e){var o=1-e,i=o*o,s=e*e,c=i*o,h=3*e*i,M=3*s*o,f=s*e;return t[0]=n[0]*c+a[0]*h+r[0]*M+u[0]*f,t[1]=n[1]*c+a[1]*h+r[1]*M+u[1]*f,t[2]=n[2]*c+a[2]*h+r[2]*M+u[2]*f,t},random:function(t,n){n=void 0===n?1:n;var a=2*r()*Math.PI,u=2*r()-1,e=Math.sqrt(1-u*u)*n;return t[0]=Math.cos(a)*e,t[1]=Math.sin(a)*e,t[2]=u*n,t},transformMat4:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=a[3]*r+a[7]*u+a[11]*e+a[15];return o=o||1,t[0]=(a[0]*r+a[4]*u+a[8]*e+a[12])/o,t[1]=(a[1]*r+a[5]*u+a[9]*e+a[13])/o,t[2]=(a[2]*r+a[6]*u+a[10]*e+a[14])/o,t},transformMat3:function(t,n,a){var r=n[0],u=n[1],e=n[2];return t[0]=r*a[0]+u*a[3]+e*a[6],t[1]=r*a[1]+u*a[4]+e*a[7],t[2]=r*a[2]+u*a[5]+e*a[8],t},transformQuat:function(t,n,a){var r=a[0],u=a[1],e=a[2],o=a[3],i=n[0],s=n[1],c=n[2],h=u*c-e*s,M=e*i-r*c,f=r*s-u*i;return h+=h,M+=M,f+=f,t[0]=i+o*h+u*f-e*M,t[1]=s+o*M+e*h-r*f,t[2]=c+o*f+r*M-u*h,t},rotateX:function(t,n,a,r){var u=[],e=[];return u[0]=n[0]-a[0],u[1]=n[1]-a[1],u[2]=n[2]-a[2],e[0]=u[0],e[1]=u[1]*Math.cos(r)-u[2]*Math.sin(r),e[2]=u[1]*Math.sin(r)+u[2]*Math.cos(r),t[0]=e[0]+a[0],t[1]=e[1]+a[1],t[2]=e[2]+a[2],t},rotateY:function(t,n,a,r){var u=[],e=[];return u[0]=n[0]-a[0],u[1]=n[1]-a[1],u[2]=n[2]-a[2],e[0]=u[2]*Math.sin(r)+u[0]*Math.cos(r),e[1]=u[1],e[2]=u[2]*Math.cos(r)-u[0]*Math.sin(r),t[0]=e[0]+a[0],t[1]=e[1]+a[1],t[2]=e[2]+a[2],t},rotateZ:function(t,n,a,r){var u=[],e=[];return u[0]=n[0]-a[0],u[1]=n[1]-a[1],u[2]=n[2]-a[2],e[0]=u[0]*Math.cos(r)-u[1]*Math.sin(r),e[1]=u[0]*Math.sin(r)+u[1]*Math.cos(r),e[2]=u[2],t[0]=e[0]+a[0],t[1]=e[1]+a[1],t[2]=e[2]+a[2],t},angle:function(t,n){var a=t[0],r=t[1],u=t[2],e=n[0],o=n[1],i=n[2],s=Math.sqrt((a*a+r*r+u*u)*(e*e+o*o+i*i)),c=s&&W(t,n)/s;return Math.acos(Math.min(Math.max(c,-1),1))},zero:function(t){return t[0]=0,t[1]=0,t[2]=0,t},str:function(t){return"vec3("+t[0]+", "+t[1]+", "+t[2]+")"},exactEquals:function(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]},equals:function(t,a){var r=t[0],u=t[1],e=t[2],o=a[0],i=a[1],s=a[2];return Math.abs(r-o)<=n*Math.max(1,Math.abs(r),Math.abs(o))&&Math.abs(u-i)<=n*Math.max(1,Math.abs(u),Math.abs(i))&&Math.abs(e-s)<=n*Math.max(1,Math.abs(e),Math.abs(s))},sub:J,mul:K,div:$,dist:tt,sqrDist:nt,len:at,sqrLen:rt,forEach:ut});function ot(){var t=new a(4);return a!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0,t[3]=0),t}function it(t){var n=new a(4);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n}function st(t,n,r,u){var e=new a(4);return e[0]=t,e[1]=n,e[2]=r,e[3]=u,e}function ct(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t}function ht(t,n,a,r,u){return t[0]=n,t[1]=a,t[2]=r,t[3]=u,t}function Mt(t,n,a){return t[0]=n[0]+a[0],t[1]=n[1]+a[1],t[2]=n[2]+a[2],t[3]=n[3]+a[3],t}function ft(t,n,a){return t[0]=n[0]-a[0],t[1]=n[1]-a[1],t[2]=n[2]-a[2],t[3]=n[3]-a[3],t}function lt(t,n,a){return t[0]=n[0]*a[0],t[1]=n[1]*a[1],t[2]=n[2]*a[2],t[3]=n[3]*a[3],t}function vt(t,n,a){return t[0]=n[0]/a[0],t[1]=n[1]/a[1],t[2]=n[2]/a[2],t[3]=n[3]/a[3],t}function bt(t,n,a){return t[0]=n[0]*a,t[1]=n[1]*a,t[2]=n[2]*a,t[3]=n[3]*a,t}function mt(t,n){var a=n[0]-t[0],r=n[1]-t[1],u=n[2]-t[2],e=n[3]-t[3];return Math.sqrt(a*a+r*r+u*u+e*e)}function dt(t,n){var a=n[0]-t[0],r=n[1]-t[1],u=n[2]-t[2],e=n[3]-t[3];return a*a+r*r+u*u+e*e}function xt(t){var n=t[0],a=t[1],r=t[2],u=t[3];return Math.sqrt(n*n+a*a+r*r+u*u)}function qt(t){var n=t[0],a=t[1],r=t[2],u=t[3];return n*n+a*a+r*r+u*u}function pt(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=a*a+r*r+u*u+e*e;return o>0&&(o=1/Math.sqrt(o)),t[0]=a*o,t[1]=r*o,t[2]=u*o,t[3]=e*o,t}function yt(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]+t[3]*n[3]}function gt(t,n,a,r){var u=n[0],e=n[1],o=n[2],i=n[3];return t[0]=u+r*(a[0]-u),t[1]=e+r*(a[1]-e),t[2]=o+r*(a[2]-o),t[3]=i+r*(a[3]-i),t}function _t(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]}var At=ft,wt=lt,zt=vt,Rt=mt,Ot=dt,jt=xt,Et=qt,Pt=function(){var t=ot();return function(n,a,r,u,e,o){var i,s;for(a||(a=4),r||(r=0),s=u?Math.min(u*a+r,n.length):n.length,i=r;i<s;i+=a)t[0]=n[i],t[1]=n[i+1],t[2]=n[i+2],t[3]=n[i+3],e(t,t,o),n[i]=t[0],n[i+1]=t[1],n[i+2]=t[2],n[i+3]=t[3];return n}}(),Tt=Object.freeze({__proto__:null,create:ot,clone:it,fromValues:st,copy:ct,set:ht,add:Mt,subtract:ft,multiply:lt,divide:vt,ceil:function(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t[2]=Math.ceil(n[2]),t[3]=Math.ceil(n[3]),t},floor:function(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t[2]=Math.floor(n[2]),t[3]=Math.floor(n[3]),t},min:function(t,n,a){return t[0]=Math.min(n[0],a[0]),t[1]=Math.min(n[1],a[1]),t[2]=Math.min(n[2],a[2]),t[3]=Math.min(n[3],a[3]),t},max:function(t,n,a){return t[0]=Math.max(n[0],a[0]),t[1]=Math.max(n[1],a[1]),t[2]=Math.max(n[2],a[2]),t[3]=Math.max(n[3],a[3]),t},round:function(t,n){return t[0]=u(n[0]),t[1]=u(n[1]),t[2]=u(n[2]),t[3]=u(n[3]),t},scale:bt,scaleAndAdd:function(t,n,a,r){return t[0]=n[0]+a[0]*r,t[1]=n[1]+a[1]*r,t[2]=n[2]+a[2]*r,t[3]=n[3]+a[3]*r,t},distance:mt,squaredDistance:dt,length:xt,squaredLength:qt,negate:function(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t[3]=-n[3],t},inverse:function(t,n){return t[0]=1/n[0],t[1]=1/n[1],t[2]=1/n[2],t[3]=1/n[3],t},normalize:pt,dot:yt,cross:function(t,n,a,r){var u=a[0]*r[1]-a[1]*r[0],e=a[0]*r[2]-a[2]*r[0],o=a[0]*r[3]-a[3]*r[0],i=a[1]*r[2]-a[2]*r[1],s=a[1]*r[3]-a[3]*r[1],c=a[2]*r[3]-a[3]*r[2],h=n[0],M=n[1],f=n[2],l=n[3];return t[0]=M*c-f*s+l*i,t[1]=-h*c+f*o-l*e,t[2]=h*s-M*o+l*u,t[3]=-h*i+M*e-f*u,t},lerp:gt,random:function(t,n){var a,u,e,o,i,s,c;n=void 0===n?1:n,i=(a=2*(c=r())-1)*a+(u=(4*r()-2)*Math.sqrt(c*-c+c))*u,s=(e=2*(c=r())-1)*e+(o=(4*r()-2)*Math.sqrt(c*-c+c))*o;var h=Math.sqrt((1-i)/s);return t[0]=n*a,t[1]=n*u,t[2]=n*e*h,t[3]=n*o*h,t},transformMat4:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3];return t[0]=a[0]*r+a[4]*u+a[8]*e+a[12]*o,t[1]=a[1]*r+a[5]*u+a[9]*e+a[13]*o,t[2]=a[2]*r+a[6]*u+a[10]*e+a[14]*o,t[3]=a[3]*r+a[7]*u+a[11]*e+a[15]*o,t},transformQuat:function(t,n,a){var r=a[0],u=a[1],e=a[2],o=a[3],i=n[0],s=n[1],c=n[2],h=u*c-e*s,M=e*i-r*c,f=r*s-u*i;return h+=h,M+=M,f+=f,t[0]=i+o*h+u*f-e*M,t[1]=s+o*M+e*h-r*f,t[2]=c+o*f+r*M-u*h,t[3]=n[3],t},zero:function(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t},str:function(t){return"vec4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"},exactEquals:_t,equals:function(t,a){var r=t[0],u=t[1],e=t[2],o=t[3],i=a[0],s=a[1],c=a[2],h=a[3];return Math.abs(r-i)<=n*Math.max(1,Math.abs(r),Math.abs(i))&&Math.abs(u-s)<=n*Math.max(1,Math.abs(u),Math.abs(s))&&Math.abs(e-c)<=n*Math.max(1,Math.abs(e),Math.abs(c))&&Math.abs(o-h)<=n*Math.max(1,Math.abs(o),Math.abs(h))},sub:At,mul:wt,div:zt,dist:Rt,sqrDist:Ot,len:jt,sqrLen:Et,forEach:Pt});function Dt(){var t=new a(4);return a!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t[3]=1,t}function St(t,n,a){a*=.5;var r=Math.sin(a);return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=Math.cos(a),t}function It(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=a[0],s=a[1],c=a[2],h=a[3];return t[0]=r*h+o*i+u*c-e*s,t[1]=u*h+o*s+e*i-r*c,t[2]=e*h+o*c+r*s-u*i,t[3]=o*h-r*i-u*s-e*c,t}function Ft(t,n,a){a*=.5;var r=n[0],u=n[1],e=n[2],o=n[3],i=Math.sin(a),s=Math.cos(a);return t[0]=r*s+o*i,t[1]=u*s+e*i,t[2]=e*s-u*i,t[3]=o*s-r*i,t}function Lt(t,n,a){a*=.5;var r=n[0],u=n[1],e=n[2],o=n[3],i=Math.sin(a),s=Math.cos(a);return t[0]=r*s-e*i,t[1]=u*s+o*i,t[2]=e*s+r*i,t[3]=o*s-u*i,t}function Vt(t,n,a){a*=.5;var r=n[0],u=n[1],e=n[2],o=n[3],i=Math.sin(a),s=Math.cos(a);return t[0]=r*s+u*i,t[1]=u*s-r*i,t[2]=e*s+o*i,t[3]=o*s-e*i,t}function kt(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=Math.sqrt(a*a+r*r+u*u),i=Math.exp(e),s=o>0?i*Math.sin(o)/o:0;return t[0]=a*s,t[1]=r*s,t[2]=u*s,t[3]=i*Math.cos(o),t}function Qt(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=Math.sqrt(a*a+r*r+u*u),i=o>0?Math.atan2(o,e)/o:0;return t[0]=a*i,t[1]=r*i,t[2]=u*i,t[3]=.5*Math.log(a*a+r*r+u*u+e*e),t}function Yt(t,a,r,u){var e,o,i,s,c,h=a[0],M=a[1],f=a[2],l=a[3],v=r[0],b=r[1],m=r[2],d=r[3];return(o=h*v+M*b+f*m+l*d)<0&&(o=-o,v=-v,b=-b,m=-m,d=-d),1-o>n?(e=Math.acos(o),i=Math.sin(e),s=Math.sin((1-u)*e)/i,c=Math.sin(u*e)/i):(s=1-u,c=u),t[0]=s*h+c*v,t[1]=s*M+c*b,t[2]=s*f+c*m,t[3]=s*l+c*d,t}function Zt(t,n){var a,r=n[0]+n[4]+n[8];if(r>0)a=Math.sqrt(r+1),t[3]=.5*a,a=.5/a,t[0]=(n[5]-n[7])*a,t[1]=(n[6]-n[2])*a,t[2]=(n[1]-n[3])*a;else{var u=0;n[4]>n[0]&&(u=1),n[8]>n[3*u+u]&&(u=2);var e=(u+1)%3,o=(u+2)%3;a=Math.sqrt(n[3*u+u]-n[3*e+e]-n[3*o+o]+1),t[u]=.5*a,a=.5/a,t[3]=(n[3*e+o]-n[3*o+e])*a,t[e]=(n[3*e+u]+n[3*u+e])*a,t[o]=(n[3*o+u]+n[3*u+o])*a}return t}var Nt=it,Xt=st,Bt=ct,Ut=ht,Gt=Mt,Wt=It,Ct=bt,Ht=yt,Jt=gt,Kt=xt,$t=Kt,tn=qt,nn=tn,an=pt,rn=_t;var un,en,on,sn,cn,hn,Mn=(un=V(),en=Q(1,0,0),on=Q(0,1,0),function(t,n,a){var r=W(n,a);return r<-.999999?(C(un,en,n),at(un)<1e-6&&C(un,on,n),G(un,un),St(t,un,Math.PI),t):r>.999999?(t[0]=0,t[1]=0,t[2]=0,t[3]=1,t):(C(un,n,a),t[0]=un[0],t[1]=un[1],t[2]=un[2],t[3]=1+r,an(t,t))}),fn=(sn=Dt(),cn=Dt(),function(t,n,a,r,u,e){return Yt(sn,n,u,e),Yt(cn,a,r,e),Yt(t,sn,cn,2*e*(1-e)),t}),ln=(hn=x(),function(t,n,a,r){return hn[0]=a[0],hn[3]=a[1],hn[6]=a[2],hn[1]=r[0],hn[4]=r[1],hn[7]=r[2],hn[2]=-n[0],hn[5]=-n[1],hn[8]=-n[2],an(t,Zt(t,hn))}),vn=Object.freeze({__proto__:null,create:Dt,identity:function(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t},setAxisAngle:St,getAxisAngle:function(t,a){var r=2*Math.acos(a[3]),u=Math.sin(r/2);return u>n?(t[0]=a[0]/u,t[1]=a[1]/u,t[2]=a[2]/u):(t[0]=1,t[1]=0,t[2]=0),r},getAngle:function(t,n){var a=Ht(t,n);return Math.acos(2*a*a-1)},multiply:It,rotateX:Ft,rotateY:Lt,rotateZ:Vt,calculateW:function(t,n){var a=n[0],r=n[1],u=n[2];return t[0]=a,t[1]=r,t[2]=u,t[3]=Math.sqrt(Math.abs(1-a*a-r*r-u*u)),t},exp:kt,ln:Qt,pow:function(t,n,a){return Qt(t,n),Ct(t,t,a),kt(t,t),t},slerp:Yt,random:function(t){var n=r(),a=r(),u=r(),e=Math.sqrt(1-n),o=Math.sqrt(n);return t[0]=e*Math.sin(2*Math.PI*a),t[1]=e*Math.cos(2*Math.PI*a),t[2]=o*Math.sin(2*Math.PI*u),t[3]=o*Math.cos(2*Math.PI*u),t},invert:function(t,n){var a=n[0],r=n[1],u=n[2],e=n[3],o=a*a+r*r+u*u+e*e,i=o?1/o:0;return t[0]=-a*i,t[1]=-r*i,t[2]=-u*i,t[3]=e*i,t},conjugate:function(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t[3]=n[3],t},fromMat3:Zt,fromEuler:function(t,n,a,r){var u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"zyx",e=Math.PI/360;n*=e,r*=e,a*=e;var o=Math.sin(n),i=Math.cos(n),s=Math.sin(a),c=Math.cos(a),h=Math.sin(r),M=Math.cos(r);switch(u){case"xyz":t[0]=o*c*M+i*s*h,t[1]=i*s*M-o*c*h,t[2]=i*c*h+o*s*M,t[3]=i*c*M-o*s*h;break;case"xzy":t[0]=o*c*M-i*s*h,t[1]=i*s*M-o*c*h,t[2]=i*c*h+o*s*M,t[3]=i*c*M+o*s*h;break;case"yxz":t[0]=o*c*M+i*s*h,t[1]=i*s*M-o*c*h,t[2]=i*c*h-o*s*M,t[3]=i*c*M+o*s*h;break;case"yzx":t[0]=o*c*M+i*s*h,t[1]=i*s*M+o*c*h,t[2]=i*c*h-o*s*M,t[3]=i*c*M-o*s*h;break;case"zxy":t[0]=o*c*M-i*s*h,t[1]=i*s*M+o*c*h,t[2]=i*c*h+o*s*M,t[3]=i*c*M-o*s*h;break;case"zyx":t[0]=o*c*M-i*s*h,t[1]=i*s*M+o*c*h,t[2]=i*c*h-o*s*M,t[3]=i*c*M+o*s*h;break;default:throw new Error("Unknown angle order "+u)}return t},str:function(t){return"quat("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"},clone:Nt,fromValues:Xt,copy:Bt,set:Ut,add:Gt,mul:Wt,scale:Ct,dot:Ht,lerp:Jt,length:Kt,len:$t,squaredLength:tn,sqrLen:nn,normalize:an,exactEquals:rn,equals:function(t,n){return Math.abs(yt(t,n))>=.999999},rotationTo:Mn,sqlerp:fn,setAxes:ln});function bn(t,n,a){var r=.5*a[0],u=.5*a[1],e=.5*a[2],o=n[0],i=n[1],s=n[2],c=n[3];return t[0]=o,t[1]=i,t[2]=s,t[3]=c,t[4]=r*c+u*s-e*i,t[5]=u*c+e*o-r*s,t[6]=e*c+r*i-u*o,t[7]=-r*o-u*i-e*s,t}function mn(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t}var dn=Bt;var xn=Bt;function qn(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=a[4],s=a[5],c=a[6],h=a[7],M=n[4],f=n[5],l=n[6],v=n[7],b=a[0],m=a[1],d=a[2],x=a[3];return t[0]=r*x+o*b+u*d-e*m,t[1]=u*x+o*m+e*b-r*d,t[2]=e*x+o*d+r*m-u*b,t[3]=o*x-r*b-u*m-e*d,t[4]=r*h+o*i+u*c-e*s+M*x+v*b+f*d-l*m,t[5]=u*h+o*s+e*i-r*c+f*x+v*m+l*b-M*d,t[6]=e*h+o*c+r*s-u*i+l*x+v*d+M*m-f*b,t[7]=o*h-r*i-u*s-e*c+v*x-M*b-f*m-l*d,t}var pn=qn;var yn=Ht;var gn=Kt,_n=gn,An=tn,wn=An;var zn=Object.freeze({__proto__:null,create:function(){var t=new a(8);return a!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0,t[4]=0,t[5]=0,t[6]=0,t[7]=0),t[3]=1,t},clone:function(t){var n=new a(8);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n},fromValues:function(t,n,r,u,e,o,i,s){var c=new a(8);return c[0]=t,c[1]=n,c[2]=r,c[3]=u,c[4]=e,c[5]=o,c[6]=i,c[7]=s,c},fromRotationTranslationValues:function(t,n,r,u,e,o,i){var s=new a(8);s[0]=t,s[1]=n,s[2]=r,s[3]=u;var c=.5*e,h=.5*o,M=.5*i;return s[4]=c*u+h*r-M*n,s[5]=h*u+M*t-c*r,s[6]=M*u+c*n-h*t,s[7]=-c*t-h*n-M*r,s},fromRotationTranslation:bn,fromTranslation:function(t,n){return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t[4]=.5*n[0],t[5]=.5*n[1],t[6]=.5*n[2],t[7]=0,t},fromRotation:function(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=0,t[5]=0,t[6]=0,t[7]=0,t},fromMat4:function(t,n){var r=Dt();j(r,n);var u=new a(3);return R(u,n),bn(t,r,u),t},copy:mn,identity:function(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t[6]=0,t[7]=0,t},set:function(t,n,a,r,u,e,o,i,s){return t[0]=n,t[1]=a,t[2]=r,t[3]=u,t[4]=e,t[5]=o,t[6]=i,t[7]=s,t},getReal:dn,getDual:function(t,n){return t[0]=n[4],t[1]=n[5],t[2]=n[6],t[3]=n[7],t},setReal:xn,setDual:function(t,n){return t[4]=n[0],t[5]=n[1],t[6]=n[2],t[7]=n[3],t},getTranslation:function(t,n){var a=n[4],r=n[5],u=n[6],e=n[7],o=-n[0],i=-n[1],s=-n[2],c=n[3];return t[0]=2*(a*c+e*o+r*s-u*i),t[1]=2*(r*c+e*i+u*o-a*s),t[2]=2*(u*c+e*s+a*i-r*o),t},translate:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=.5*a[0],s=.5*a[1],c=.5*a[2],h=n[4],M=n[5],f=n[6],l=n[7];return t[0]=r,t[1]=u,t[2]=e,t[3]=o,t[4]=o*i+u*c-e*s+h,t[5]=o*s+e*i-r*c+M,t[6]=o*c+r*s-u*i+f,t[7]=-r*i-u*s-e*c+l,t},rotateX:function(t,n,a){var r=-n[0],u=-n[1],e=-n[2],o=n[3],i=n[4],s=n[5],c=n[6],h=n[7],M=i*o+h*r+s*e-c*u,f=s*o+h*u+c*r-i*e,l=c*o+h*e+i*u-s*r,v=h*o-i*r-s*u-c*e;return Ft(t,n,a),r=t[0],u=t[1],e=t[2],o=t[3],t[4]=M*o+v*r+f*e-l*u,t[5]=f*o+v*u+l*r-M*e,t[6]=l*o+v*e+M*u-f*r,t[7]=v*o-M*r-f*u-l*e,t},rotateY:function(t,n,a){var r=-n[0],u=-n[1],e=-n[2],o=n[3],i=n[4],s=n[5],c=n[6],h=n[7],M=i*o+h*r+s*e-c*u,f=s*o+h*u+c*r-i*e,l=c*o+h*e+i*u-s*r,v=h*o-i*r-s*u-c*e;return Lt(t,n,a),r=t[0],u=t[1],e=t[2],o=t[3],t[4]=M*o+v*r+f*e-l*u,t[5]=f*o+v*u+l*r-M*e,t[6]=l*o+v*e+M*u-f*r,t[7]=v*o-M*r-f*u-l*e,t},rotateZ:function(t,n,a){var r=-n[0],u=-n[1],e=-n[2],o=n[3],i=n[4],s=n[5],c=n[6],h=n[7],M=i*o+h*r+s*e-c*u,f=s*o+h*u+c*r-i*e,l=c*o+h*e+i*u-s*r,v=h*o-i*r-s*u-c*e;return Vt(t,n,a),r=t[0],u=t[1],e=t[2],o=t[3],t[4]=M*o+v*r+f*e-l*u,t[5]=f*o+v*u+l*r-M*e,t[6]=l*o+v*e+M*u-f*r,t[7]=v*o-M*r-f*u-l*e,t},rotateByQuatAppend:function(t,n,a){var r=a[0],u=a[1],e=a[2],o=a[3],i=n[0],s=n[1],c=n[2],h=n[3];return t[0]=i*o+h*r+s*e-c*u,t[1]=s*o+h*u+c*r-i*e,t[2]=c*o+h*e+i*u-s*r,t[3]=h*o-i*r-s*u-c*e,i=n[4],s=n[5],c=n[6],h=n[7],t[4]=i*o+h*r+s*e-c*u,t[5]=s*o+h*u+c*r-i*e,t[6]=c*o+h*e+i*u-s*r,t[7]=h*o-i*r-s*u-c*e,t},rotateByQuatPrepend:function(t,n,a){var r=n[0],u=n[1],e=n[2],o=n[3],i=a[0],s=a[1],c=a[2],h=a[3];return t[0]=r*h+o*i+u*c-e*s,t[1]=u*h+o*s+e*i-r*c,t[2]=e*h+o*c+r*s-u*i,t[3]=o*h-r*i-u*s-e*c,i=a[4],s=a[5],c=a[6],h=a[7],t[4]=r*h+o*i+u*c-e*s,t[5]=u*h+o*s+e*i-r*c,t[6]=e*h+o*c+r*s-u*i,t[7]=o*h-r*i-u*s-e*c,t},rotateAroundAxis:function(t,a,r,u){if(Math.abs(u)<n)return mn(t,a);var e=Math.sqrt(r[0]*r[0]+r[1]*r[1]+r[2]*r[2]);u*=.5;var o=Math.sin(u),i=o*r[0]/e,s=o*r[1]/e,c=o*r[2]/e,h=Math.cos(u),M=a[0],f=a[1],l=a[2],v=a[3];t[0]=M*h+v*i+f*c-l*s,t[1]=f*h+v*s+l*i-M*c,t[2]=l*h+v*c+M*s-f*i,t[3]=v*h-M*i-f*s-l*c;var b=a[4],m=a[5],d=a[6],x=a[7];return t[4]=b*h+x*i+m*c-d*s,t[5]=m*h+x*s+d*i-b*c,t[6]=d*h+x*c+b*s-m*i,t[7]=x*h-b*i-m*s-d*c,t},add:function(t,n,a){return t[0]=n[0]+a[0],t[1]=n[1]+a[1],t[2]=n[2]+a[2],t[3]=n[3]+a[3],t[4]=n[4]+a[4],t[5]=n[5]+a[5],t[6]=n[6]+a[6],t[7]=n[7]+a[7],t},multiply:qn,mul:pn,scale:function(t,n,a){return t[0]=n[0]*a,t[1]=n[1]*a,t[2]=n[2]*a,t[3]=n[3]*a,t[4]=n[4]*a,t[5]=n[5]*a,t[6]=n[6]*a,t[7]=n[7]*a,t},dot:yn,lerp:function(t,n,a,r){var u=1-r;return yn(n,a)<0&&(r=-r),t[0]=n[0]*u+a[0]*r,t[1]=n[1]*u+a[1]*r,t[2]=n[2]*u+a[2]*r,t[3]=n[3]*u+a[3]*r,t[4]=n[4]*u+a[4]*r,t[5]=n[5]*u+a[5]*r,t[6]=n[6]*u+a[6]*r,t[7]=n[7]*u+a[7]*r,t},invert:function(t,n){var a=An(n);return t[0]=-n[0]/a,t[1]=-n[1]/a,t[2]=-n[2]/a,t[3]=n[3]/a,t[4]=-n[4]/a,t[5]=-n[5]/a,t[6]=-n[6]/a,t[7]=n[7]/a,t},conjugate:function(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t[3]=n[3],t[4]=-n[4],t[5]=-n[5],t[6]=-n[6],t[7]=n[7],t},length:gn,len:_n,squaredLength:An,sqrLen:wn,normalize:function(t,n){var a=An(n);if(a>0){a=Math.sqrt(a);var r=n[0]/a,u=n[1]/a,e=n[2]/a,o=n[3]/a,i=n[4],s=n[5],c=n[6],h=n[7],M=r*i+u*s+e*c+o*h;t[0]=r,t[1]=u,t[2]=e,t[3]=o,t[4]=(i-r*M)/a,t[5]=(s-u*M)/a,t[6]=(c-e*M)/a,t[7]=(h-o*M)/a}return t},str:function(t){return"quat2("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+")"},exactEquals:function(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]},equals:function(t,a){var r=t[0],u=t[1],e=t[2],o=t[3],i=t[4],s=t[5],c=t[6],h=t[7],M=a[0],f=a[1],l=a[2],v=a[3],b=a[4],m=a[5],d=a[6],x=a[7];return Math.abs(r-M)<=n*Math.max(1,Math.abs(r),Math.abs(M))&&Math.abs(u-f)<=n*Math.max(1,Math.abs(u),Math.abs(f))&&Math.abs(e-l)<=n*Math.max(1,Math.abs(e),Math.abs(l))&&Math.abs(o-v)<=n*Math.max(1,Math.abs(o),Math.abs(v))&&Math.abs(i-b)<=n*Math.max(1,Math.abs(i),Math.abs(b))&&Math.abs(s-m)<=n*Math.max(1,Math.abs(s),Math.abs(m))&&Math.abs(c-d)<=n*Math.max(1,Math.abs(c),Math.abs(d))&&Math.abs(h-x)<=n*Math.max(1,Math.abs(h),Math.abs(x))}});function Rn(){var t=new a(2);return a!=Float32Array&&(t[0]=0,t[1]=0),t}function On(t,n,a){return t[0]=n[0]-a[0],t[1]=n[1]-a[1],t}function jn(t,n,a){return t[0]=n[0]*a[0],t[1]=n[1]*a[1],t}function En(t,n,a){return t[0]=n[0]/a[0],t[1]=n[1]/a[1],t}function Pn(t,n){var a=n[0]-t[0],r=n[1]-t[1];return Math.sqrt(a*a+r*r)}function Tn(t,n){var a=n[0]-t[0],r=n[1]-t[1];return a*a+r*r}function Dn(t){var n=t[0],a=t[1];return Math.sqrt(n*n+a*a)}function Sn(t){var n=t[0],a=t[1];return n*n+a*a}var In=Dn,Fn=On,Ln=jn,Vn=En,kn=Pn,Qn=Tn,Yn=Sn,Zn=function(){var t=Rn();return function(n,a,r,u,e,o){var i,s;for(a||(a=2),r||(r=0),s=u?Math.min(u*a+r,n.length):n.length,i=r;i<s;i+=a)t[0]=n[i],t[1]=n[i+1],e(t,t,o),n[i]=t[0],n[i+1]=t[1];return n}}(),Nn=Object.freeze({__proto__:null,create:Rn,clone:function(t){var n=new a(2);return n[0]=t[0],n[1]=t[1],n},fromValues:function(t,n){var r=new a(2);return r[0]=t,r[1]=n,r},copy:function(t,n){return t[0]=n[0],t[1]=n[1],t},set:function(t,n,a){return t[0]=n,t[1]=a,t},add:function(t,n,a){return t[0]=n[0]+a[0],t[1]=n[1]+a[1],t},subtract:On,multiply:jn,divide:En,ceil:function(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t},floor:function(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t},min:function(t,n,a){return t[0]=Math.min(n[0],a[0]),t[1]=Math.min(n[1],a[1]),t},max:function(t,n,a){return t[0]=Math.max(n[0],a[0]),t[1]=Math.max(n[1],a[1]),t},round:function(t,n){return t[0]=u(n[0]),t[1]=u(n[1]),t},scale:function(t,n,a){return t[0]=n[0]*a,t[1]=n[1]*a,t},scaleAndAdd:function(t,n,a,r){return t[0]=n[0]+a[0]*r,t[1]=n[1]+a[1]*r,t},distance:Pn,squaredDistance:Tn,length:Dn,squaredLength:Sn,negate:function(t,n){return t[0]=-n[0],t[1]=-n[1],t},inverse:function(t,n){return t[0]=1/n[0],t[1]=1/n[1],t},normalize:function(t,n){var a=n[0],r=n[1],u=a*a+r*r;return u>0&&(u=1/Math.sqrt(u)),t[0]=n[0]*u,t[1]=n[1]*u,t},dot:function(t,n){return t[0]*n[0]+t[1]*n[1]},cross:function(t,n,a){var r=n[0]*a[1]-n[1]*a[0];return t[0]=t[1]=0,t[2]=r,t},lerp:function(t,n,a,r){var u=n[0],e=n[1];return t[0]=u+r*(a[0]-u),t[1]=e+r*(a[1]-e),t},random:function(t,n){n=void 0===n?1:n;var a=2*r()*Math.PI;return t[0]=Math.cos(a)*n,t[1]=Math.sin(a)*n,t},transformMat2:function(t,n,a){var r=n[0],u=n[1];return t[0]=a[0]*r+a[2]*u,t[1]=a[1]*r+a[3]*u,t},transformMat2d:function(t,n,a){var r=n[0],u=n[1];return t[0]=a[0]*r+a[2]*u+a[4],t[1]=a[1]*r+a[3]*u+a[5],t},transformMat3:function(t,n,a){var r=n[0],u=n[1];return t[0]=a[0]*r+a[3]*u+a[6],t[1]=a[1]*r+a[4]*u+a[7],t},transformMat4:function(t,n,a){var r=n[0],u=n[1];return t[0]=a[0]*r+a[4]*u+a[12],t[1]=a[1]*r+a[5]*u+a[13],t},rotate:function(t,n,a,r){var u=n[0]-a[0],e=n[1]-a[1],o=Math.sin(r),i=Math.cos(r);return t[0]=u*i-e*o+a[0],t[1]=u*o+e*i+a[1],t},angle:function(t,n){var a=t[0],r=t[1],u=n[0],e=n[1];return Math.abs(Math.atan2(r*u-a*e,a*u+r*e))},signedAngle:function(t,n){var a=t[0],r=t[1],u=n[0],e=n[1];return Math.atan2(a*e-r*u,a*u+r*e)},zero:function(t){return t[0]=0,t[1]=0,t},str:function(t){return"vec2("+t[0]+", "+t[1]+")"},exactEquals:function(t,n){return t[0]===n[0]&&t[1]===n[1]},equals:function(t,a){var r=t[0],u=t[1],e=a[0],o=a[1];return Math.abs(r-e)<=n*Math.max(1,Math.abs(r),Math.abs(e))&&Math.abs(u-o)<=n*Math.max(1,Math.abs(u),Math.abs(o))},len:In,sub:Fn,mul:Ln,div:Vn,dist:kn,sqrDist:Qn,sqrLen:Yn,forEach:Zn});t.glMatrix=i,t.mat2=f,t.mat2d=d,t.mat3=_,t.mat4=L,t.quat=vn,t.quat2=zn,t.vec2=Nn,t.vec3=et,t.vec4=Tt,Object.defineProperty(t,"__esModule",{value:!0})}));
