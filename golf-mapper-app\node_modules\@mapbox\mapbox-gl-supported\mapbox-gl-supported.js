!function(e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).mapboxgl=e()}(function(){return function r(o,i,u){function a(t,e){if(!i[t]){if(!o[t]){var n="function"==typeof require&&require;if(!e&&n)return n(t,!0);if(f)return f(t,!0);throw(e=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",e}n=i[t]={exports:{}},o[t][0].call(n.exports,function(e){return a(o[t][1][e]||e)},n,n.exports,r,o,i,u)}return i[t].exports}for(var f="function"==typeof require&&require,e=0;e<u.length;e++)a(u[e]);return a}({1:[function(e,t,n){"use strict";function r(e){return!o(e)}function o(e){var t,n;return"undefined"==typeof window||"undefined"==typeof document?"not a browser":function(){if(!("Worker"in window&&"Blob"in window&&"URL"in window))return;var t,e,n=new Blob([""],{type:"text/javascript"}),n=URL.createObjectURL(n);try{e=new Worker(n),t=!0}catch(e){t=!1}e&&e.terminate();return URL.revokeObjectURL(n),t}()?((t=document.createElement("canvas")).width=t.height=1,(n=(n=t.getContext("2d"))&&n.getImageData(0,0,1,1))&&n.width===t.width?function(e){void 0===i[e]&&(i[e]=function(e){var t,e=function(e){var t=document.createElement("canvas"),n=Object.create(r.webGLContextAttributes);return n.failIfMajorPerformanceCaveat=e,t.getContext("webgl2",n)}(e);if(!e)return!1;try{t=e.createShader(e.VERTEX_SHADER)}catch(e){return!1}return!(!t||e.isContextLost())&&(e.shaderSource(t,"void main() {}"),e.compileShader(t),!0===e.getShaderParameter(t,e.COMPILE_STATUS))}(e));return i[e]}(e&&e.failIfMajorPerformanceCaveat)?document.documentMode?"insufficient ECMAScript 6 support":void 0:"insufficient WebGL2 support":"insufficient Canvas/getImageData support"):"insufficient worker support"}n.supported=r,n.notSupportedReason=o;var i={};r.webGLContextAttributes={antialias:!1,alpha:!0,stencil:!0,depth:!0}},{}]},{},[1])(1)});