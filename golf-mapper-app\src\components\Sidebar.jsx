import { Link, useLocation } from 'react-router-dom'

const Sidebar = () => {
  const location = useLocation()

  const isActive = (path) => {
    return location.pathname === path ? 'active' : ''
  }

  return (
    <aside className="sidebar">
      <nav>
        <ul>
          <li>
            <Link to="/" className={isActive('/')}>
              🏠 Home
            </Link>
          </li>
          <li>
            <Link to="/create" className={isActive('/create')}>
              ➕ Create Course
            </Link>
          </li>
          <li>
            <Link to="/courses" className={isActive('/courses')}>
              📋 Courses
            </Link>
          </li>
          <li>
            <Link to="/about" className={isActive('/about')}>
              ℹ️ About
            </Link>
          </li>
        </ul>
      </nav>
    </aside>
  )
}

export default Sidebar
