{"name": "pbf", "version": "4.0.1", "description": "a low-level, lightweight protocol buffers implementation in JavaScript", "main": "index.js", "type": "module", "exports": {".": "./index.js", "./compile": "./compile.js"}, "scripts": {"bench": "node bench/bench.js", "pretest": "eslint *.js compile.js test/*.js test/fixtures/*.js bin/pbf", "test": "tsc && node --test", "cov": "node --test --experimental-test-covetage", "build": "rollup -c", "prepublishOnly": "npm run test && npm run build"}, "files": ["bin", "dist", "compile.js", "index.d.ts"], "types": "index.d.ts", "bin": {"pbf": "bin/pbf"}, "repository": {"type": "git", "url": "**************:mapbox/pbf.git"}, "keywords": ["protocol", "buffer", "pbf", "protobuf", "binary", "format", "serialization", "encoder", "decoder"], "author": "<PERSON>", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/mapbox/pbf/issues"}, "homepage": "https://github.com/mapbox/pbf", "dependencies": {"resolve-protobuf-schema": "^2.1.0"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.4", "benchmark": "^2.1.4", "eslint": "^9.6.0", "eslint-config-mourner": "^4.0.1", "protobufjs": "^7.3.2", "protocol-buffers": "^5.0.0", "rollup": "^4.18.0", "tile-stats-runner": "^1.0.0", "typescript": "^5.5.3"}}