import { useState } from 'react'

const CourseControls = () => {
  const [courseName, setCourseName] = useState('')
  const [courseDescription, setCourseDescription] = useState('')

  const handleSaveCourse = () => {
    if (!courseName.trim()) {
      alert('Please enter a course name')
      return
    }
    
    console.log('Saving course:', { courseName, courseDescription })
    // Future: Save to database/local storage
    alert('Course saved successfully!')
  }

  return (
    <div style={{ marginBottom: '1rem', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
      <h3 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Course Information</h3>
      
      <div style={{ marginBottom: '1rem' }}>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
          Course Name:
        </label>
        <input
          type="text"
          value={courseName}
          onChange={(e) => setCourseName(e.target.value)}
          placeholder="Enter course name..."
          style={{
            width: '100%',
            padding: '0.5rem',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '1rem'
          }}
        />
      </div>

      <div style={{ marginBottom: '1rem' }}>
        <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
          Description:
        </label>
        <textarea
          value={courseDescription}
          onChange={(e) => setCourseDescription(e.target.value)}
          placeholder="Enter course description..."
          rows={3}
          style={{
            width: '100%',
            padding: '0.5rem',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '1rem',
            resize: 'vertical'
          }}
        />
      </div>

      <button
        onClick={handleSaveCourse}
        style={{
          backgroundColor: '#27ae60',
          color: 'white',
          border: 'none',
          padding: '0.75rem 1.5rem',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '1rem'
        }}
      >
        💾 Save Course
      </button>
    </div>
  )
}

export default CourseControls
