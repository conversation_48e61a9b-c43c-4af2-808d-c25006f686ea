{"name": "protocol-buffers-schema", "version": "3.6.0", "description": "No nonsense protocol buffers schema parser written in Javascript", "main": "index.js", "devDependencies": {"standard": "^10.0.3", "tape": "^4.8.0"}, "scripts": {"test": "standard && tape test/*.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/protocol-buffers-schema"}, "keywords": ["protobuf", "protocol", "buffers", "schema", "parser", "parse"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/protocol-buffers-schema/issues"}, "homepage": "https://github.com/mafintosh/protocol-buffers-schema"}