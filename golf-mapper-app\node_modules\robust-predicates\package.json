{"name": "robust-predicates", "version": "2.0.4", "description": "Fast robust predicates for computational geometry", "keywords": ["computational geometry", "robust arithmetic"], "author": "<PERSON>", "license": "Unlicense", "main": "umd/predicates.js", "unpkg": "umd/predicates.min.js", "module": "index.js", "types": "index.d.ts", "scripts": {"build": "mkdirp esm && node compile.js", "lint": "eslint *.js src test/test.js", "test": "npm run lint && npm run build && node -r esm test/test.js", "cov": "rm -rf node_modules/.cache/esm && nyc --require esm -r lcov -r text node test/test.js", "bench": "node -r esm bench.js", "prepublishOnly": "npm run test && rollup -c"}, "devDependencies": {"eslint": "^6.3.0", "eslint-config-mourner": "^3.0.0", "esm": "^3.2.25", "mkdirp": "^0.5.1", "nextafter": "^1.0.0", "nyc": "^14.1.1", "robust-in-sphere": "^1.1.3", "robust-orientation": "^1.1.3", "rollup": "^1.20.3", "rollup-plugin-terser": "^5.1.1", "tape": "^4.11.0", "terser": "^4.2.1"}, "files": ["index.js", "esm", "umd"], "eslintConfig": {"extends": "mourner", "rules": {"camelcase": 0, "new-cap": 0, "no-unused-vars": [2, {"varsIgnorePattern": "splitter|bvirt|c|[ab]hi|[ab]lo|_[ijk0]|u3|[st][01]"}], "no-lonely-if": 0}, "globals": {"$Fast_Two_Sum": false, "$Two_Sum": false, "$Two_Diff_Tail": false, "$Split": false, "$Two_Product": false, "$Two_Product_Presplit": false, "$Two_One_Product": false, "$Cross_Product": false, "$Square_Sum": false, "$Two_Product_Sum": false}}}