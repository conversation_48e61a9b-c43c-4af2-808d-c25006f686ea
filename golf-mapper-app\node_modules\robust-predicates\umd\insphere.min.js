!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t=t||self).predicates={})}(this,function(t){"use strict";const n=134217729,e=33306690738754706e-32;function r(t,n,e,r,o){let a,s,u,f,c=n[0],i=r[0],h=0,b=0;i>c==i>-c?(a=c,c=n[++h]):(a=i,i=r[++b]);let M=0;if(h<t&&b<e)for(i>c==i>-c?(u=a-((s=c+a)-c),c=n[++h]):(u=a-((s=i+a)-i),i=r[++b]),a=s,0!==u&&(o[M++]=u);h<t&&b<e;)i>c==i>-c?(u=a-((s=a+c)-(f=s-a))+(c-f),c=n[++h]):(u=a-((s=a+i)-(f=s-a))+(i-f),i=r[++b]),a=s,0!==u&&(o[M++]=u);for(;h<t;)u=a-((s=a+c)-(f=s-a))+(c-f),c=n[++h],a=s,0!==u&&(o[M++]=u);for(;b<e;)u=a-((s=a+i)-(f=s-a))+(i-f),i=r[++b],a=s,0!==u&&(o[M++]=u);return 0===a&&0!==M||(o[M++]=a),M}function o(t,n,e,o,a,s,u,f){return r(r(t,n,e,o,u),u,a,s,f)}function a(t,e,r,o){let a,s,u,f,c,i,h,b,M,l,d;d=r-(l=(h=n*r)-(h-r));let p=e[0],y=0;0!==(u=(M=p-(b=(h=n*p)-(h-p)))*d-((a=p*r)-b*l-M*l-b*d))&&(o[y++]=u);for(let x=1;x<t;x++)0!==(u=a-((s=a+(c=(M=(p=e[x])-(b=(h=n*p)-(h-p)))*d-((f=p*r)-b*l-M*l-b*d)))-(i=s-a))+(c-i))&&(o[y++]=u),0!==(u=s-((a=f+s)-f))&&(o[y++]=u);return 0===a&&0!==y||(o[y++]=a),y}function s(t,n){for(let e=0;e<t;e++)n[e]=-n[e];return t}function u(t){return new Float64Array(t)}const f=17763568394002532e-31,c=5551115123125792e-31,i=8751425667295619e-46,h=u(4),b=u(4),M=u(4),l=u(4),d=u(4),p=u(4),y=u(4),x=u(4),j=u(4),m=u(4),_=u(24),v=u(24),w=u(24),A=u(24),F=u(24),O=u(24),P=u(24),g=u(24),k=u(24),q=u(24),z=u(1152),B=u(1152),C=u(1152),D=u(1152),E=u(1152),G=u(2304),H=u(2304),I=u(3456),J=u(5760),K=u(8),L=u(8),N=u(8),Q=u(16),R=u(24),S=u(48),T=u(48),U=u(96),V=u(192),W=u(384),X=u(384),Y=u(384),Z=u(768);function $(t,n,e,r,s,u,f){return o(a(4,t,r,K),K,a(4,n,s,L),L,a(4,e,u,N),N,Q,f)}function tt(t,n,e,u,f,c,i,h,b,M,l,d){const p=r(r(t,n,e,u,S),S,s(r(f,c,i,h,T),T),T,U);return o(a(a(p,U,b,V),V,b,W),W,a(a(p,U,M,V),V,M,X),X,a(a(p,U,l,V),V,l,Y),Y,Z,d)}const nt=u(96),et=u(96),rt=u(96),ot=u(1152);function at(t,n,e,r,s,u,f,c,i,h){const b=$(t,n,e,r,s,u,R);return o(a(a(b,R,f,S),S,f,nt),nt,a(a(b,R,c,S),S,c,et),et,a(a(b,R,i,S),S,i,rt),rt,V,h)}function st(t,a,u,f,K,L,N,Q,R,S,T,U,V,W,X,Y){let Z,nt,et,rt,st,ut,ft,ct,it,ht,bt,Mt,lt,dt,pt,yt,xt,jt,mt,_t,vt,wt,At,Ft,Ot,Pt,gt,kt,qt,zt,Bt;const Ct=t-V,Dt=f-V,Et=N-V,Gt=S-V,Ht=a-W,It=K-W,Jt=Q-W,Kt=T-W,Lt=u-X,Nt=L-X,Qt=R-X,Rt=U-X;mt=(qt=(wt=Ct-(vt=(_t=n*Ct)-(_t-Ct)))*(Ft=It-(At=(_t=n*It)-(_t-It)))-((kt=Ct*It)-vt*At-wt*At-vt*Ft))-(Ot=qt-(Bt=(wt=Dt-(vt=(_t=n*Dt)-(_t-Dt)))*(Ft=Ht-(At=(_t=n*Ht)-(_t-Ht)))-((zt=Dt*Ht)-vt*At-wt*At-vt*Ft))),h[0]=qt-(Ot+mt)+(mt-Bt),mt=(gt=kt-((Pt=kt+Ot)-(mt=Pt-kt))+(Ot-mt))-(Ot=gt-zt),h[1]=gt-(Ot+mt)+(mt-zt),mt=(Z=Pt+Ot)-Pt,h[2]=Pt-(Z-mt)+(Ot-mt),h[3]=Z,mt=(qt=(wt=Dt-(vt=(_t=n*Dt)-(_t-Dt)))*(Ft=Jt-(At=(_t=n*Jt)-(_t-Jt)))-((kt=Dt*Jt)-vt*At-wt*At-vt*Ft))-(Ot=qt-(Bt=(wt=Et-(vt=(_t=n*Et)-(_t-Et)))*(Ft=It-(At=(_t=n*It)-(_t-It)))-((zt=Et*It)-vt*At-wt*At-vt*Ft))),b[0]=qt-(Ot+mt)+(mt-Bt),mt=(gt=kt-((Pt=kt+Ot)-(mt=Pt-kt))+(Ot-mt))-(Ot=gt-zt),b[1]=gt-(Ot+mt)+(mt-zt),mt=(nt=Pt+Ot)-Pt,b[2]=Pt-(nt-mt)+(Ot-mt),b[3]=nt,mt=(qt=(wt=Et-(vt=(_t=n*Et)-(_t-Et)))*(Ft=Kt-(At=(_t=n*Kt)-(_t-Kt)))-((kt=Et*Kt)-vt*At-wt*At-vt*Ft))-(Ot=qt-(Bt=(wt=Gt-(vt=(_t=n*Gt)-(_t-Gt)))*(Ft=Jt-(At=(_t=n*Jt)-(_t-Jt)))-((zt=Gt*Jt)-vt*At-wt*At-vt*Ft))),M[0]=qt-(Ot+mt)+(mt-Bt),mt=(gt=kt-((Pt=kt+Ot)-(mt=Pt-kt))+(Ot-mt))-(Ot=gt-zt),M[1]=gt-(Ot+mt)+(mt-zt),mt=(et=Pt+Ot)-Pt,M[2]=Pt-(et-mt)+(Ot-mt),M[3]=et,mt=(qt=(wt=Gt-(vt=(_t=n*Gt)-(_t-Gt)))*(Ft=Ht-(At=(_t=n*Ht)-(_t-Ht)))-((kt=Gt*Ht)-vt*At-wt*At-vt*Ft))-(Ot=qt-(Bt=(wt=Ct-(vt=(_t=n*Ct)-(_t-Ct)))*(Ft=Kt-(At=(_t=n*Kt)-(_t-Kt)))-((zt=Ct*Kt)-vt*At-wt*At-vt*Ft))),j[0]=qt-(Ot+mt)+(mt-Bt),mt=(gt=kt-((Pt=kt+Ot)-(mt=Pt-kt))+(Ot-mt))-(Ot=gt-zt),j[1]=gt-(Ot+mt)+(mt-zt),mt=(rt=Pt+Ot)-Pt,j[2]=Pt-(rt-mt)+(Ot-mt),j[3]=rt,mt=(qt=(wt=Ct-(vt=(_t=n*Ct)-(_t-Ct)))*(Ft=Jt-(At=(_t=n*Jt)-(_t-Jt)))-((kt=Ct*Jt)-vt*At-wt*At-vt*Ft))-(Ot=qt-(Bt=(wt=Et-(vt=(_t=n*Et)-(_t-Et)))*(Ft=Ht-(At=(_t=n*Ht)-(_t-Ht)))-((zt=Et*Ht)-vt*At-wt*At-vt*Ft))),p[0]=qt-(Ot+mt)+(mt-Bt),mt=(gt=kt-((Pt=kt+Ot)-(mt=Pt-kt))+(Ot-mt))-(Ot=gt-zt),p[1]=gt-(Ot+mt)+(mt-zt),mt=(st=Pt+Ot)-Pt,p[2]=Pt-(st-mt)+(Ot-mt),p[3]=st,mt=(qt=(wt=Dt-(vt=(_t=n*Dt)-(_t-Dt)))*(Ft=Kt-(At=(_t=n*Kt)-(_t-Kt)))-((kt=Dt*Kt)-vt*At-wt*At-vt*Ft))-(Ot=qt-(Bt=(wt=Gt-(vt=(_t=n*Gt)-(_t-Gt)))*(Ft=It-(At=(_t=n*It)-(_t-It)))-((zt=Gt*It)-vt*At-wt*At-vt*Ft))),y[0]=qt-(Ot+mt)+(mt-Bt),mt=(gt=kt-((Pt=kt+Ot)-(mt=Pt-kt))+(Ot-mt))-(Ot=gt-zt),y[1]=gt-(Ot+mt)+(mt-zt),mt=(ut=Pt+Ot)-Pt,y[2]=Pt-(ut-mt)+(Ot-mt),y[3]=ut;let St=function(t,n){let e=n[0];for(let r=1;r<t;r++)e+=n[r];return e}(r(r(s(at(b,M,y,Rt,Nt,-Qt,Ct,Ht,Lt,z),z),z,at(M,j,p,Lt,Qt,Rt,Dt,It,Nt,B),B,G),G,r(s(at(j,h,y,Nt,Rt,Lt,Et,Jt,Qt,C),C),C,at(h,b,p,Qt,Lt,-Nt,Gt,Kt,Rt,D),D,H),H,ot),ot),Tt=c*Y;if(St>=Tt||-St>=Tt)return St;if(ft=t-(Ct+(mt=t-Ct))+(mt-V),bt=a-(Ht+(mt=a-Ht))+(mt-W),pt=u-(Lt+(mt=u-Lt))+(mt-X),ct=f-(Dt+(mt=f-Dt))+(mt-V),Mt=K-(It+(mt=K-It))+(mt-W),yt=L-(Nt+(mt=L-Nt))+(mt-X),it=N-(Et+(mt=N-Et))+(mt-V),lt=Q-(Jt+(mt=Q-Jt))+(mt-W),xt=R-(Qt+(mt=R-Qt))+(mt-X),ht=S-(Gt+(mt=S-Gt))+(mt-V),dt=T-(Kt+(mt=T-Kt))+(mt-W),jt=U-(Rt+(mt=U-Rt))+(mt-X),0===ft&&0===bt&&0===pt&&0===ct&&0===Mt&&0===yt&&0===it&&0===lt&&0===xt&&0===ht&&0===dt&&0===jt)return St;Tt=i*Y+e*Math.abs(St);const Ut=Ct*Mt+It*ft-(Ht*ct+Dt*bt),Vt=Dt*lt+Jt*ct-(It*it+Et*Mt),Wt=Et*dt+Kt*it-(Jt*ht+Gt*lt),Xt=Gt*bt+Ht*ht-(Kt*ft+Ct*dt),Yt=Ct*lt+Jt*ft-(Ht*it+Et*bt),Zt=Dt*dt+Kt*ct-(It*ht+Gt*Mt);return(St+=(Dt*Dt+It*It+Nt*Nt)*(Qt*Xt+Rt*Yt+Lt*Wt+(xt*rt+jt*st+pt*et))+(Gt*Gt+Kt*Kt+Rt*Rt)*(Lt*Vt-Nt*Yt+Qt*Ut+(pt*nt-yt*st+xt*Z))-((Ct*Ct+Ht*Ht+Lt*Lt)*(Nt*Wt-Qt*Zt+Rt*Vt+(yt*et-xt*ut+jt*nt))+(Et*Et+Jt*Jt+Qt*Qt)*(Rt*Ut+Lt*Zt+Nt*Xt+(jt*Z+pt*ut+yt*rt)))+2*((Dt*ct+It*Mt+Nt*yt)*(Qt*rt+Rt*st+Lt*et)+(Gt*ht+Kt*dt+Rt*jt)*(Lt*nt-Nt*st+Qt*Z)-((Ct*ft+Ht*bt+Lt*pt)*(Nt*et-Qt*ut+Rt*nt)+(Et*it+Jt*lt+Qt*xt)*(Rt*Z+Lt*ut+Nt*rt))))>=Tt||-St>=Tt?St:function(t,e,r,a,s,u,f,c,i,K,L,N,Q,R,S){let T,U,V,W,X,Y,Z,nt,et,rt,ot,at,st,ut;T=(ot=(W=t-(V=(U=n*t)-(U-t)))*(Y=s-(X=(U=n*s)-(U-s)))-((rt=t*s)-V*X-W*X-V*Y))-(Z=ot-(st=(W=a-(V=(U=n*a)-(U-a)))*(Y=e-(X=(U=n*e)-(U-e)))-((at=a*e)-V*X-W*X-V*Y))),h[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),h[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,h[2]=nt-(ut-T)+(Z-T),h[3]=ut,T=(ot=(W=a-(V=(U=n*a)-(U-a)))*(Y=c-(X=(U=n*c)-(U-c)))-((rt=a*c)-V*X-W*X-V*Y))-(Z=ot-(st=(W=f-(V=(U=n*f)-(U-f)))*(Y=s-(X=(U=n*s)-(U-s)))-((at=f*s)-V*X-W*X-V*Y))),b[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),b[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,b[2]=nt-(ut-T)+(Z-T),b[3]=ut,T=(ot=(W=f-(V=(U=n*f)-(U-f)))*(Y=L-(X=(U=n*L)-(U-L)))-((rt=f*L)-V*X-W*X-V*Y))-(Z=ot-(st=(W=K-(V=(U=n*K)-(U-K)))*(Y=c-(X=(U=n*c)-(U-c)))-((at=K*c)-V*X-W*X-V*Y))),M[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),M[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,M[2]=nt-(ut-T)+(Z-T),M[3]=ut,T=(ot=(W=K-(V=(U=n*K)-(U-K)))*(Y=R-(X=(U=n*R)-(U-R)))-((rt=K*R)-V*X-W*X-V*Y))-(Z=ot-(st=(W=Q-(V=(U=n*Q)-(U-Q)))*(Y=L-(X=(U=n*L)-(U-L)))-((at=Q*L)-V*X-W*X-V*Y))),l[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),l[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,l[2]=nt-(ut-T)+(Z-T),l[3]=ut,T=(ot=(W=Q-(V=(U=n*Q)-(U-Q)))*(Y=e-(X=(U=n*e)-(U-e)))-((rt=Q*e)-V*X-W*X-V*Y))-(Z=ot-(st=(W=t-(V=(U=n*t)-(U-t)))*(Y=R-(X=(U=n*R)-(U-R)))-((at=t*R)-V*X-W*X-V*Y))),d[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),d[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,d[2]=nt-(ut-T)+(Z-T),d[3]=ut,T=(ot=(W=t-(V=(U=n*t)-(U-t)))*(Y=c-(X=(U=n*c)-(U-c)))-((rt=t*c)-V*X-W*X-V*Y))-(Z=ot-(st=(W=f-(V=(U=n*f)-(U-f)))*(Y=e-(X=(U=n*e)-(U-e)))-((at=f*e)-V*X-W*X-V*Y))),p[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),p[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,p[2]=nt-(ut-T)+(Z-T),p[3]=ut,T=(ot=(W=a-(V=(U=n*a)-(U-a)))*(Y=L-(X=(U=n*L)-(U-L)))-((rt=a*L)-V*X-W*X-V*Y))-(Z=ot-(st=(W=K-(V=(U=n*K)-(U-K)))*(Y=s-(X=(U=n*s)-(U-s)))-((at=K*s)-V*X-W*X-V*Y))),y[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),y[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,y[2]=nt-(ut-T)+(Z-T),y[3]=ut,T=(ot=(W=f-(V=(U=n*f)-(U-f)))*(Y=R-(X=(U=n*R)-(U-R)))-((rt=f*R)-V*X-W*X-V*Y))-(Z=ot-(st=(W=Q-(V=(U=n*Q)-(U-Q)))*(Y=c-(X=(U=n*c)-(U-c)))-((at=Q*c)-V*X-W*X-V*Y))),x[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),x[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,x[2]=nt-(ut-T)+(Z-T),x[3]=ut,T=(ot=(W=K-(V=(U=n*K)-(U-K)))*(Y=e-(X=(U=n*e)-(U-e)))-((rt=K*e)-V*X-W*X-V*Y))-(Z=ot-(st=(W=t-(V=(U=n*t)-(U-t)))*(Y=L-(X=(U=n*L)-(U-L)))-((at=t*L)-V*X-W*X-V*Y))),j[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),j[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,j[2]=nt-(ut-T)+(Z-T),j[3]=ut,T=(ot=(W=Q-(V=(U=n*Q)-(U-Q)))*(Y=s-(X=(U=n*s)-(U-s)))-((rt=Q*s)-V*X-W*X-V*Y))-(Z=ot-(st=(W=a-(V=(U=n*a)-(U-a)))*(Y=R-(X=(U=n*R)-(U-R)))-((at=a*R)-V*X-W*X-V*Y))),m[0]=ot-(Z+T)+(T-st),T=(et=rt-((nt=rt+Z)-(T=nt-rt))+(Z-T))-(Z=et-at),m[1]=et-(Z+T)+(T-at),T=(ut=nt+Z)-nt,m[2]=nt-(ut-T)+(Z-T),m[3]=ut;const ft=$(h,b,p,i,r,-u,_),ct=$(b,M,y,N,u,-i,v),it=$(M,l,x,S,i,-N,w),ht=$(l,d,j,r,N,-S,A),bt=$(d,h,m,u,S,-r,F),Mt=$(h,y,j,N,r,u,O),lt=$(b,x,m,S,u,i,P),dt=$(M,j,p,r,i,N,g),pt=$(l,m,y,u,N,S,k),yt=$(d,p,x,i,S,r,q),xt=o(tt(it,w,lt,P,pt,k,ct,v,t,e,r,z),z,tt(ht,A,dt,g,yt,q,it,w,a,s,u,B),B,o(tt(bt,F,pt,k,Mt,O,ht,A,f,c,i,C),C,tt(ft,_,yt,q,lt,P,bt,F,K,L,N,D),D,tt(ct,v,Mt,O,dt,g,ft,_,Q,R,S,E),E,H,I),I,G,J);return J[xt-1]}(t,a,u,f,K,L,N,Q,R,S,T,U,V,W,X)}t.insphere=function(t,n,e,r,o,a,s,u,c,i,h,b,M,l,d){const p=t-M,y=r-M,x=s-M,j=i-M,m=n-l,_=o-l,v=u-l,w=h-l,A=e-d,F=a-d,O=c-d,P=b-d,g=p*_,k=y*m,q=g-k,z=y*v,B=x*_,C=z-B,D=x*w,E=j*v,G=D-E,H=j*m,I=p*w,J=H-I,K=p*v,L=x*m,N=K-L,Q=y*w,R=j*_,S=Q-R,T=p*p+m*m+A*A,U=y*y+_*_+F*F,V=x*x+v*v+O*O,W=j*j+w*w+P*P,X=V*(P*q+A*S+F*J)-W*(A*C-F*N+O*q)+(T*(F*G-O*S+P*C)-U*(O*J+P*N+A*G)),Y=Math.abs(A),Z=Math.abs(F),$=Math.abs(O),tt=Math.abs(P),nt=Math.abs(g),et=Math.abs(k),rt=Math.abs(z),ot=Math.abs(B),at=Math.abs(D),ut=Math.abs(E),ft=Math.abs(H),ct=Math.abs(I),it=Math.abs(K),ht=Math.abs(L),bt=Math.abs(Q),Mt=Math.abs(R),lt=((at+ut)*Z+(Mt+bt)*$+(rt+ot)*tt)*T+((ft+ct)*$+(it+ht)*tt+(at+ut)*Y)*U+((nt+et)*tt+(bt+Mt)*Y+(ft+ct)*Z)*V+((rt+ot)*Y+(ht+it)*Z+(nt+et)*$)*W,dt=f*lt;return X>dt||-X>dt?X:-st(t,n,e,r,o,a,s,u,c,i,h,b,M,l,d,lt)},t.inspherefast=function(t,n,e,r,o,a,s,u,f,c,i,h,b,M,l){const d=t-b,p=r-b,y=s-b,x=c-b,j=n-M,m=o-M,_=u-M,v=i-M,w=e-l,A=a-l,F=f-l,O=h-l,P=d*m-p*j,g=p*_-y*m,k=y*v-x*_,q=x*j-d*v,z=d*_-y*j,B=p*v-x*m;return(y*y+_*_+F*F)*(O*P+w*B+A*q)-(x*x+v*v+O*O)*(w*g-A*z+F*P)+((d*d+j*j+w*w)*(A*k-F*B+O*g)-(p*p+m*m+A*A)*(F*q+O*z+w*k))},Object.defineProperty(t,"__esModule",{value:!0})});
